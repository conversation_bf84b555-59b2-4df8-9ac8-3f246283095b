<Window x:Class="SimpleBinanceRanking.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:SimpleBinanceRanking"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:viewmodels="clr-namespace:SimpleBinanceRanking.ViewModels"
        xmlns:converters="clr-namespace:SimpleBinanceRanking.ValueConverters"
        mc:Ignorable="d"
        Title="{Binding WindowTitle}" Height="1550" Width="1720"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource WindowBackgroundBrush}"
        TextElement.Foreground="{DynamicResource WindowForegroundBrush}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="14"
        FontFamily="Microsoft YaHei"
        KeyboardNavigation.TabNavigation="None"
        Focusable="True"
        FocusManager.FocusedElement="{Binding ElementName=MainGrid}"
        TextOptions.TextRenderingMode="ClearType"
        TextOptions.TextFormattingMode="Display"
        TextOptions.TextHintingMode="Auto"
        RenderOptions.ClearTypeHint="Enabled"
        RenderOptions.EdgeMode="Unspecified"
        UseLayoutRounding="True"
        SnapsToDevicePixels="True"
        Loaded="Window_Loaded">

    <Window.Resources>
        <!-- 值转换器 -->
        <converters:PercentageColorConverter x:Key="PercentageColorConverter"/>
        <converters:PercentageFormatConverter x:Key="PercentageFormatConverter"/>

        <!-- 自定义附加属性，用于标识ListViewItem所属的ListView -->
        <local:ListViewHelper x:Key="ListViewHelper"/>

        <!-- 通用的ListViewItem控件模板 -->
        <ControlTemplate x:Key="RankingItemTemplate" TargetType="{x:Type ListViewItem}">
            <Grid>
                <!-- 左侧指示条，默认隐藏 -->
                <Border x:Name="IndicatorBorder" Width="3" HorizontalAlignment="Left"
                        Background="Transparent" Margin="0,0,0,0"/>
                <Border x:Name="MainBorder" BorderThickness="{TemplateBinding BorderThickness}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        Background="{TemplateBinding Background}" Margin="3,0,0,0">
                    <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                      Margin="{TemplateBinding Padding}"/>
                </Border>
            </Grid>
            <ControlTemplate.Triggers>
                <!-- 高亮时显示指示条 -->
                <DataTrigger Binding="{Binding IsHighlighted}" Value="True">
                    <Setter TargetName="IndicatorBorder" Property="Background" Value="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Tag}"/>
                </DataTrigger>
            </ControlTemplate.Triggers>
        </ControlTemplate>

        <!-- 基础列表项样式，带左侧指示条 -->
        <Style x:Key="RankingItemStyle" TargetType="ListViewItem">
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0,0,0,1"/>
            <Setter Property="BorderBrush" Value="#151515"/>
            <Setter Property="Template" Value="{StaticResource RankingItemTemplate}"/>
            <EventSetter Event="MouseEnter" Handler="ListViewItem_MouseEnter"/>
            <EventSetter Event="MouseLeave" Handler="ListViewItem_MouseLeave"/>
        </Style>

        <!-- 涨幅榜样式 -->
        <Style x:Key="GreenRankingItemStyle" TargetType="ListViewItem" BasedOn="{StaticResource RankingItemStyle}">
            <Setter Property="Tag" Value="{StaticResource GreenColor}"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{StaticResource GreenColorHover}"/>
                </Trigger>
                <DataTrigger Binding="{Binding IsHighlighted}" Value="True">
                    <Setter Property="Background" Value="{StaticResource GreenColorHover}"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- 跌幅榜样式 -->
        <Style x:Key="RedRankingItemStyle" TargetType="ListViewItem" BasedOn="{StaticResource RankingItemStyle}">
            <Setter Property="Tag" Value="{StaticResource RedColor}"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{StaticResource RedColorHover}"/>
                </Trigger>
                <DataTrigger Binding="{Binding IsHighlighted}" Value="True">
                    <Setter Property="Background" Value="{StaticResource RedColorHover}"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- 振幅榜样式 -->
        <Style x:Key="YellowRankingItemStyle" TargetType="ListViewItem" BasedOn="{StaticResource RankingItemStyle}">
            <Setter Property="Tag" Value="{StaticResource YellowColor}"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{StaticResource YellowColorHover}"/>
                </Trigger>
                <DataTrigger Binding="{Binding IsHighlighted}" Value="True">
                    <Setter Property="Background" Value="{StaticResource YellowColorHover}"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- 成交额榜样式 -->
        <Style x:Key="BlueRankingItemStyle" TargetType="ListViewItem" BasedOn="{StaticResource RankingItemStyle}">
            <Setter Property="Tag" Value="{StaticResource CmcBlueColor}"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{StaticResource CmcBlueColorHover}"/>
                </Trigger>
                <DataTrigger Binding="{Binding IsHighlighted}" Value="True">
                    <Setter Property="Background" Value="{StaticResource CmcBlueColorHover}"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- 成交笔数榜样式 -->
        <Style x:Key="PurpleRankingItemStyle" TargetType="ListViewItem" BasedOn="{StaticResource RankingItemStyle}">
            <Setter Property="Tag" Value="{StaticResource CountColor}"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{StaticResource CountColorHover}"/>
                </Trigger>
                <DataTrigger Binding="{Binding IsHighlighted}" Value="True">
                    <Setter Property="Background" Value="{StaticResource CountColorHover}"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid x:Name="MainGrid">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 顶部工具栏 -->
        <Border Background="#0B0E11" Height="50">
            <DockPanel>
                <materialDesign:PackIcon Kind="Bitcoin" Height="24" Width="24" VerticalAlignment="Center" Margin="16,0,0,0" Foreground="#F0B90B"/>
                <TextBlock Text="BINANCE合约行情" FontSize="18" FontWeight="Bold" VerticalAlignment="Center" Margin="12,0,0,0" Foreground="{StaticResource PrimaryTextColor}"/>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,0,16,0">
                    <!-- 交易对搜索输入框 -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,20,0">
                        <materialDesign:PackIcon Kind="Magnify" VerticalAlignment="Center" Margin="0,0,6,0" Foreground="{StaticResource SecondaryTextColor}" Height="16" Width="16"/>

                        <!-- 简单底部线条容器 -->
                        <Grid Width="156" Height="36" VerticalAlignment="Center">
                            <!-- 主输入框 -->
                            <TextBox x:Name="SymbolSearchTextBox"
                                     Background="#1E1E1E"
                                     BorderThickness="0"
                                     Foreground="{StaticResource PrimaryTextColor}"
                                     FontSize="14"
                                     FontWeight="Medium"
                                     Padding="6,0,0,0"
                                     Margin="0"
                                     VerticalAlignment="Stretch"
                                     HorizontalAlignment="Stretch"
                                     VerticalContentAlignment="Center"
                                     HorizontalContentAlignment="Left"
                                     TextAlignment="Left"
                                     CharacterCasing="Upper"
                                     materialDesign:HintAssist.Hint="搜索交易对"
                                     materialDesign:HintAssist.Foreground="{StaticResource SecondaryTextColor}"
                                     TextChanged="SymbolSearchTextBox_TextChanged"
                                     GotFocus="SymbolSearchTextBox_GotFocus"
                                     LostFocus="SymbolSearchTextBox_LostFocus">
                                <TextBox.Style>
                                    <Style TargetType="TextBox" BasedOn="{StaticResource {x:Type TextBox}}">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="TextBox">
                                                    <Border Background="{TemplateBinding Background}"
                                                            CornerRadius="5,5,0,0"
                                                            Padding="0">
                                                        <ScrollViewer x:Name="PART_ContentHost"
                                                                      VerticalAlignment="Center"
                                                                      HorizontalAlignment="Left"
                                                                      Margin="{TemplateBinding Padding}"
                                                                      Padding="0"
                                                                      Background="Transparent"
                                                                      BorderThickness="0"
                                                                      VerticalScrollBarVisibility="Hidden"
                                                                      HorizontalScrollBarVisibility="Hidden"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </TextBox.Style>
                            </TextBox>

                            <!-- 底部彩色跑马灯线条 -->
                            <Border x:Name="BottomColorLine"
                                    Height="2"
                                    VerticalAlignment="Bottom"
                                    HorizontalAlignment="Stretch">
                                <Border.Background>
                                    <LinearGradientBrush x:Name="BottomFlowingGradient" StartPoint="0,0" EndPoint="1,0">
                                        <GradientStop x:Name="BottomColor1" Color="#FF6B9D" Offset="0"/>
                                        <GradientStop x:Name="BottomColor2" Color="#FF8A5B" Offset="0.125"/>
                                        <GradientStop x:Name="BottomColor3" Color="#FFB347" Offset="0.25"/>
                                        <GradientStop x:Name="BottomColor4" Color="#FFD700" Offset="0.375"/>
                                        <GradientStop x:Name="BottomColor5" Color="#9ACD32" Offset="0.5"/>
                                        <GradientStop x:Name="BottomColor6" Color="#40E0D0" Offset="0.625"/>
                                        <GradientStop x:Name="BottomColor7" Color="#6495ED" Offset="0.75"/>
                                        <GradientStop x:Name="BottomColor8" Color="#9370DB" Offset="0.875"/>
                                        <GradientStop x:Name="BottomColor9" Color="#FF6B9D" Offset="1"/>
                                    </LinearGradientBrush>
                                </Border.Background>

                                <!-- 底部线条跑马灯动画 -->
                                <Border.Triggers>
                                    <EventTrigger RoutedEvent="Border.Loaded">
                                        <BeginStoryboard x:Name="BottomFlowAnimation">
                                            <Storyboard RepeatBehavior="Forever">
                                                <!-- 8种颜色从左到右跑马灯效果 -->
                                                <DoubleAnimation Storyboard.TargetName="BottomColor1" Storyboard.TargetProperty="Offset" From="0" To="1" Duration="0:0:3"/>
                                                <DoubleAnimation Storyboard.TargetName="BottomColor2" Storyboard.TargetProperty="Offset" From="0.125" To="1.125" Duration="0:0:3"/>
                                                <DoubleAnimation Storyboard.TargetName="BottomColor3" Storyboard.TargetProperty="Offset" From="0.25" To="1.25" Duration="0:0:3"/>
                                                <DoubleAnimation Storyboard.TargetName="BottomColor4" Storyboard.TargetProperty="Offset" From="0.375" To="1.375" Duration="0:0:3"/>
                                                <DoubleAnimation Storyboard.TargetName="BottomColor5" Storyboard.TargetProperty="Offset" From="0.5" To="1.5" Duration="0:0:3"/>
                                                <DoubleAnimation Storyboard.TargetName="BottomColor6" Storyboard.TargetProperty="Offset" From="0.625" To="1.625" Duration="0:0:3"/>
                                                <DoubleAnimation Storyboard.TargetName="BottomColor7" Storyboard.TargetProperty="Offset" From="0.75" To="1.75" Duration="0:0:3"/>
                                                <DoubleAnimation Storyboard.TargetName="BottomColor8" Storyboard.TargetProperty="Offset" From="0.875" To="1.875" Duration="0:0:3"/>
                                                <DoubleAnimation Storyboard.TargetName="BottomColor9" Storyboard.TargetProperty="Offset" From="1" To="2" Duration="0:0:3"/>
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </EventTrigger>
                                </Border.Triggers>
                            </Border>
                        </Grid>
                    </StackPanel>

                    <!-- WebSocket连接状态指示器 -->
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,12,0">
                        <Ellipse x:Name="ConnectionStatusIndicator" Width="12" Height="12" Fill="Red" Margin="0,0,6,0">
                            <Ellipse.Effect>
                                <DropShadowEffect ShadowDepth="0" BlurRadius="4" Color="#FF0000" Opacity="0.5"/>
                            </Ellipse.Effect>
                        </Ellipse>
                        <TextBlock x:Name="ConnectionStatusText" Text="未连接" VerticalAlignment="Center" Foreground="{StaticResource PrimaryTextColor}" FontSize="12"/>
                    </StackPanel>
                    <Button x:Name="DisconnectButton" Background="#2C2C2C" BorderBrush="#2C2C2C" Content="断开连接" Click="DisconnectButton_Click" Height="30" Width="100"/>
                </StackPanel>
            </DockPanel>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="12">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 涨幅榜 -->
            <Border Grid.Column="0" Margin="6" CornerRadius="8" Background="#161A1E" BorderBrush="#222222" BorderThickness="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="36"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Border Grid.Row="0" Background="#161A1E" BorderThickness="0,0,0,1" BorderBrush="#292F33">
                        <DockPanel Margin="12,0" VerticalAlignment="Center" Height="36">
                            <materialDesign:PackIcon Kind="TrendingUp" Foreground="{StaticResource GreenColor}" VerticalAlignment="Center" Height="18" Width="18"/>
                            <TextBlock Text="涨幅榜" FontWeight="Bold" Margin="8,0,0,0" VerticalAlignment="Center" FontSize="14" Foreground="{StaticResource PrimaryTextColor}"/>
                        </DockPanel>
                    </Border>

                    <!-- 表头行 -->
                    <Grid Grid.Row="1" Margin="8,8,8,0" Height="24">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="30"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="#" Foreground="{StaticResource SecondaryTextColor}" FontSize="12" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="1" Text="交易对" Foreground="{StaticResource SecondaryTextColor}" FontSize="12" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="2" Text="价格" Margin="8,0" HorizontalAlignment="Right" VerticalAlignment="Center" Foreground="{StaticResource SecondaryTextColor}" FontSize="12" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="3" Text="涨幅(%)" Margin="32,0,0,0" HorizontalAlignment="Right" VerticalAlignment="Center" Foreground="{StaticResource SecondaryTextColor}" FontSize="12" FontWeight="SemiBold"/>
                    </Grid>

                    <ListView Grid.Row="2" x:Name="PriceChangePercentRankingList" ItemsSource="{Binding PriceChangePercentRankingList}"
                              Background="Transparent" BorderThickness="0" SelectionChanged="RankingList_SelectionChanged"
                              ScrollViewer.VerticalScrollBarVisibility="Hidden" Padding="0"
                              VirtualizingPanel.IsVirtualizing="True"
                              VirtualizingPanel.VirtualizationMode="Recycling"
                              VirtualizingPanel.CacheLengthUnit="Page"
                              VirtualizingPanel.CacheLength="2,2"
                              VirtualizingPanel.ScrollUnit="Pixel"
                              ScrollViewer.IsDeferredScrollingEnabled="True"
                              ScrollViewer.CanContentScroll="True"
                              Focusable="True"
                              KeyboardNavigation.DirectionalNavigation="None"
                              KeyboardNavigation.TabNavigation="None"
                              local:ListViewHelper.ListViewName="PriceChangePercentRankingList"
                              Loaded="ListView_Loaded">
                        <ListView.CacheMode>
                            <BitmapCache EnableClearType="True" RenderAtScale="1.0" SnapsToDevicePixels="True" />
                        </ListView.CacheMode>
                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem" BasedOn="{StaticResource GreenRankingItemStyle}"/>
                        </ListView.ItemContainerStyle>
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Grid RenderOptions.CachingHint="Cache" RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True">
                                    <Grid.CacheMode>
                                        <BitmapCache EnableClearType="True" RenderAtScale="1.0" SnapsToDevicePixels="True" />
                                    </Grid.CacheMode>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="30"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBlock Grid.Column="0" Text="{Binding Rank, StringFormat={}\{0:D2\}}"
                                               Foreground="{StaticResource SecondaryTextColor}" FontSize="12" VerticalAlignment="Center"
                                               RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True" />
                                    <TextBlock Grid.Column="1" Text="{Binding Symbol}"
                                               FontWeight="SemiBold" FontSize="13" VerticalAlignment="Center" Foreground="{StaticResource PrimaryTextColor}"
                                               RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True" />
                                    <TextBlock Grid.Column="2" Text="{Binding Price, StringFormat=N3}"
                                               Margin="8,0" HorizontalAlignment="Right" VerticalAlignment="Center" Foreground="{StaticResource SecondaryTextColor}"
                                               RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True" />
                                    <TextBlock Grid.Column="3" Text="{Binding Value, Converter={StaticResource PercentageFormatConverter}}"
                                               Margin="16,0,0,0" Foreground="{Binding Value, Converter={StaticResource PercentageColorConverter}}" HorizontalAlignment="Right" FontWeight="SemiBold" VerticalAlignment="Center"
                                               RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True" />
                                </Grid>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                        <ListView.ItemsPanel>
                            <ItemsPanelTemplate>
                                <VirtualizingStackPanel VirtualizingPanel.IsVirtualizing="True"
                                                       VirtualizingPanel.VirtualizationMode="Recycling"
                                                       VirtualizingPanel.CacheLengthUnit="Page"
                                                       VirtualizingPanel.CacheLength="2,2"
                                                       RenderOptions.CachingHint="Cache" />
                            </ItemsPanelTemplate>
                        </ListView.ItemsPanel>
                    </ListView>
                </Grid>
            </Border>

            <!-- 跌幅榜 -->
            <Border Grid.Column="1" Margin="6" CornerRadius="8" Background="#161A1E" BorderBrush="#222222" BorderThickness="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="36"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Border Grid.Row="0" Background="#161A1E" BorderThickness="0,0,0,1" BorderBrush="#292F33">
                        <DockPanel Margin="12,0" VerticalAlignment="Center" Height="36">
                            <materialDesign:PackIcon Kind="TrendingDown" Foreground="{StaticResource RedColor}" VerticalAlignment="Center" Height="18" Width="18"/>
                            <TextBlock Text="跌幅榜" FontWeight="Bold" Margin="8,0,0,0" VerticalAlignment="Center" FontSize="14" Foreground="{StaticResource PrimaryTextColor}"/>
                        </DockPanel>
                    </Border>

                    <!-- 表头行 -->
                    <Grid Grid.Row="1" Margin="8,8,8,0" Height="24">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="30"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="#" Foreground="{StaticResource SecondaryTextColor}" FontSize="12" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="1" Text="交易对" Foreground="{StaticResource SecondaryTextColor}" FontSize="12" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="2" Text="价格" Margin="8,0" HorizontalAlignment="Right" VerticalAlignment="Center" Foreground="{StaticResource SecondaryTextColor}" FontSize="12" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="3" Text="跌幅(%)" Margin="32,0,0,0" HorizontalAlignment="Right" VerticalAlignment="Center" Foreground="{StaticResource SecondaryTextColor}" FontSize="12" FontWeight="SemiBold"/>
                    </Grid>

                    <ListView Grid.Row="2" x:Name="PriceChangePercentDescRankingList" ItemsSource="{Binding PriceChangePercentDescRankingList}"
                              Background="Transparent" BorderThickness="0" SelectionChanged="RankingList_SelectionChanged"
                              ScrollViewer.VerticalScrollBarVisibility="Hidden" Padding="0"
                              VirtualizingPanel.IsVirtualizing="True"
                              VirtualizingPanel.VirtualizationMode="Recycling"
                              VirtualizingPanel.CacheLengthUnit="Page"
                              VirtualizingPanel.CacheLength="2,2"
                              VirtualizingPanel.ScrollUnit="Pixel"
                              ScrollViewer.IsDeferredScrollingEnabled="True"
                              ScrollViewer.CanContentScroll="True"
                              Focusable="True"
                              KeyboardNavigation.DirectionalNavigation="None"
                              KeyboardNavigation.TabNavigation="None"
                              local:ListViewHelper.ListViewName="PriceChangePercentDescRankingList"
                              Loaded="ListView_Loaded">
                        <ListView.CacheMode>
                            <BitmapCache EnableClearType="True" RenderAtScale="1.0" SnapsToDevicePixels="True" />
                        </ListView.CacheMode>
                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem" BasedOn="{StaticResource RedRankingItemStyle}"/>
                        </ListView.ItemContainerStyle>
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Grid RenderOptions.CachingHint="Cache" RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True">
                                    <Grid.CacheMode>
                                        <BitmapCache EnableClearType="True" RenderAtScale="1.0" SnapsToDevicePixels="True" />
                                    </Grid.CacheMode>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="30"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBlock Grid.Column="0" Text="{Binding Rank, StringFormat={}\{0:D2\}}"
                                               Foreground="{StaticResource SecondaryTextColor}" FontSize="12" VerticalAlignment="Center"
                                               RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True" />
                                    <TextBlock Grid.Column="1" Text="{Binding Symbol}"
                                               FontWeight="SemiBold" FontSize="13" VerticalAlignment="Center" Foreground="{StaticResource PrimaryTextColor}"
                                               RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True" />
                                    <TextBlock Grid.Column="2" Text="{Binding Price, StringFormat=N3}"
                                               Margin="8,0" HorizontalAlignment="Right" VerticalAlignment="Center" Foreground="{StaticResource SecondaryTextColor}"
                                               RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True" />
                                    <TextBlock Grid.Column="3" Text="{Binding Value, Converter={StaticResource PercentageFormatConverter}}"
                                               Margin="16,0,0,0" Foreground="{Binding Value, Converter={StaticResource PercentageColorConverter}}" HorizontalAlignment="Right" FontWeight="SemiBold" VerticalAlignment="Center"
                                               RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True" />
                                </Grid>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                        <ListView.ItemsPanel>
                            <ItemsPanelTemplate>
                                <VirtualizingStackPanel VirtualizingPanel.IsVirtualizing="True"
                                                       VirtualizingPanel.VirtualizationMode="Recycling"
                                                       VirtualizingPanel.CacheLengthUnit="Page"
                                                       VirtualizingPanel.CacheLength="2,2"
                                                       RenderOptions.CachingHint="Cache" />
                            </ItemsPanelTemplate>
                        </ListView.ItemsPanel>
                    </ListView>
                </Grid>
            </Border>

            <!-- 振幅榜 -->
            <Border Grid.Column="2" Margin="6" CornerRadius="8" Background="#161A1E" BorderBrush="#222222" BorderThickness="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="36"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Border Grid.Row="0" Background="#161A1E" BorderThickness="0,0,0,1" BorderBrush="#292F33">
                        <DockPanel Margin="12,0" VerticalAlignment="Center" Height="36">
                            <materialDesign:PackIcon Kind="SwapVertical" Foreground="{StaticResource YellowColor}" VerticalAlignment="Center" Height="18" Width="18"/>
                            <TextBlock Text="振幅榜" FontWeight="Bold" Margin="8,0,0,0" VerticalAlignment="Center" FontSize="14" Foreground="{StaticResource PrimaryTextColor}"/>
                        </DockPanel>
                    </Border>

                    <!-- 表头行 -->
                    <Grid Grid.Row="1" Margin="8,8,8,0" Height="24">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="30"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="#" Foreground="{StaticResource SecondaryTextColor}" FontSize="12" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="1" Text="交易对" Foreground="{StaticResource SecondaryTextColor}" FontSize="12" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="2" Text="价格" Margin="8,0" HorizontalAlignment="Right" VerticalAlignment="Center" Foreground="{StaticResource SecondaryTextColor}" FontSize="12" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="3" Text="振幅(%)" Margin="32,0,0,0" HorizontalAlignment="Right" VerticalAlignment="Center" Foreground="{StaticResource SecondaryTextColor}" FontSize="12" FontWeight="SemiBold"/>
                    </Grid>

                    <ListView Grid.Row="2" x:Name="PriceVolatilityRankingList" ItemsSource="{Binding PriceVolatilityRankingList}"
                              Background="Transparent" BorderThickness="0" SelectionChanged="RankingList_SelectionChanged"
                              ScrollViewer.VerticalScrollBarVisibility="Hidden" Padding="0"
                              VirtualizingPanel.IsVirtualizing="True"
                              VirtualizingPanel.VirtualizationMode="Recycling"
                              VirtualizingPanel.CacheLengthUnit="Page"
                              VirtualizingPanel.CacheLength="2,2"
                              VirtualizingPanel.ScrollUnit="Pixel"
                              ScrollViewer.IsDeferredScrollingEnabled="True"
                              ScrollViewer.CanContentScroll="True"
                              Focusable="True"
                              KeyboardNavigation.DirectionalNavigation="None"
                              KeyboardNavigation.TabNavigation="None"
                              local:ListViewHelper.ListViewName="PriceVolatilityRankingList"
                              Loaded="ListView_Loaded">
                        <ListView.CacheMode>
                            <BitmapCache EnableClearType="True" RenderAtScale="1.0" SnapsToDevicePixels="True" />
                        </ListView.CacheMode>
                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem" BasedOn="{StaticResource YellowRankingItemStyle}"/>
                        </ListView.ItemContainerStyle>
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Grid RenderOptions.CachingHint="Cache" RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True">
                                    <Grid.CacheMode>
                                        <BitmapCache EnableClearType="True" RenderAtScale="1.0" SnapsToDevicePixels="True" />
                                    </Grid.CacheMode>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="30"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBlock Grid.Column="0" Text="{Binding Rank, StringFormat={}\{0:D2\}}"
                                               Foreground="{StaticResource SecondaryTextColor}" FontSize="12" VerticalAlignment="Center"
                                               RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True" />
                                    <TextBlock Grid.Column="1" Text="{Binding Symbol}"
                                               FontWeight="SemiBold" FontSize="13" VerticalAlignment="Center" Foreground="{StaticResource PrimaryTextColor}"
                                               RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True" />
                                    <TextBlock Grid.Column="2" Text="{Binding Price, StringFormat=N3}"
                                               Margin="8,0" HorizontalAlignment="Right" VerticalAlignment="Center" Foreground="{StaticResource SecondaryTextColor}"
                                               RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True" />
                                    <TextBlock Grid.Column="3" Text="{Binding Value, StringFormat={}{0:N2}%}"
                                               Margin="16,0,0,0" Foreground="{StaticResource YellowColor}" HorizontalAlignment="Right" FontWeight="SemiBold" VerticalAlignment="Center"
                                               RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True" />
                                </Grid>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                        <ListView.ItemsPanel>
                            <ItemsPanelTemplate>
                                <VirtualizingStackPanel VirtualizingPanel.IsVirtualizing="True"
                                                       VirtualizingPanel.VirtualizationMode="Recycling"
                                                       VirtualizingPanel.CacheLengthUnit="Page"
                                                       VirtualizingPanel.CacheLength="2,2"
                                                       RenderOptions.CachingHint="Cache" />
                            </ItemsPanelTemplate>
                        </ListView.ItemsPanel>
                    </ListView>
                </Grid>
            </Border>

            <!-- 成交额榜 -->
            <Border Grid.Column="3" Margin="6" CornerRadius="8" Background="#161A1E" BorderBrush="#222222" BorderThickness="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="36"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Border Grid.Row="0" Background="#161A1E" BorderThickness="0,0,0,1" BorderBrush="#292F33">
                        <DockPanel Margin="12,0" VerticalAlignment="Center" Height="36">
                            <materialDesign:PackIcon Kind="CurrencyUsd" Foreground="{StaticResource CmcBlueColor}" VerticalAlignment="Center" Height="18" Width="18"/>
                            <TextBlock Text="成交额榜" FontWeight="Bold" Margin="8,0,0,0" VerticalAlignment="Center" FontSize="14" Foreground="{StaticResource PrimaryTextColor}"/>
                        </DockPanel>
                    </Border>

                    <!-- 表头行 -->
                    <Grid Grid.Row="1" Margin="8,8,8,0" Height="24">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="30"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="#" Foreground="{StaticResource SecondaryTextColor}" FontSize="12" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="1" Text="交易对" Foreground="{StaticResource SecondaryTextColor}" FontSize="12" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="2" Text="价格" Margin="8,0" HorizontalAlignment="Right" VerticalAlignment="Center" Foreground="{StaticResource SecondaryTextColor}" FontSize="12" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="3" Text="成交额(B$)" Margin="32,0,0,0" HorizontalAlignment="Right" VerticalAlignment="Center" Foreground="{StaticResource SecondaryTextColor}" FontSize="12" FontWeight="SemiBold"/>
                    </Grid>

                    <ListView Grid.Row="2" x:Name="VolumeRankingList" ItemsSource="{Binding VolumeRankingList}"
                              Background="Transparent" BorderThickness="0" SelectionChanged="RankingList_SelectionChanged"
                              ScrollViewer.VerticalScrollBarVisibility="Hidden" Padding="0"
                              VirtualizingPanel.IsVirtualizing="True"
                              VirtualizingPanel.VirtualizationMode="Recycling"
                              VirtualizingPanel.CacheLengthUnit="Page"
                              VirtualizingPanel.CacheLength="2,2"
                              VirtualizingPanel.ScrollUnit="Pixel"
                              ScrollViewer.IsDeferredScrollingEnabled="True"
                              ScrollViewer.CanContentScroll="True"
                              Focusable="True"
                              KeyboardNavigation.DirectionalNavigation="None"
                              KeyboardNavigation.TabNavigation="None"
                              local:ListViewHelper.ListViewName="VolumeRankingList"
                              Loaded="ListView_Loaded">
                        <ListView.CacheMode>
                            <BitmapCache EnableClearType="True" RenderAtScale="1.0" SnapsToDevicePixels="True" />
                        </ListView.CacheMode>
                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem" BasedOn="{StaticResource BlueRankingItemStyle}"/>
                        </ListView.ItemContainerStyle>
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Grid RenderOptions.CachingHint="Cache" RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True">
                                    <Grid.CacheMode>
                                        <BitmapCache EnableClearType="True" RenderAtScale="1.0" SnapsToDevicePixels="True" />
                                    </Grid.CacheMode>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="30"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBlock Grid.Column="0" Text="{Binding Rank, StringFormat={}\{0:D2\}}"
                                               Foreground="{StaticResource SecondaryTextColor}" FontSize="12" VerticalAlignment="Center"
                                               RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True" />
                                    <TextBlock Grid.Column="1" Text="{Binding Symbol}"
                                               FontWeight="SemiBold" FontSize="13" VerticalAlignment="Center" Foreground="{StaticResource PrimaryTextColor}"
                                               RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True" />
                                    <TextBlock Grid.Column="2" Text="{Binding Price, StringFormat=N3}"
                                               Margin="8,0" HorizontalAlignment="Right" VerticalAlignment="Center" Foreground="{StaticResource SecondaryTextColor}"
                                               RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True" />
                                    <TextBlock Grid.Column="3" Text="{Binding Value, StringFormat={}${0:0.00}B}"
                                               Margin="16,0,0,0" Foreground="{StaticResource CmcBlueColor}" HorizontalAlignment="Right" FontWeight="SemiBold" VerticalAlignment="Center"
                                               RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True" />
                                </Grid>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                        <ListView.ItemsPanel>
                            <ItemsPanelTemplate>
                                <VirtualizingStackPanel VirtualizingPanel.IsVirtualizing="True"
                                                       VirtualizingPanel.VirtualizationMode="Recycling"
                                                       VirtualizingPanel.CacheLengthUnit="Page"
                                                       VirtualizingPanel.CacheLength="2,2"
                                                       RenderOptions.CachingHint="Cache" />
                            </ItemsPanelTemplate>
                        </ListView.ItemsPanel>
                    </ListView>
                </Grid>
            </Border>

            <!-- 成交笔数榜 -->
            <Border Grid.Column="4" Margin="6" CornerRadius="8" Background="#161A1E" BorderBrush="#222222" BorderThickness="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="36"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Border Grid.Row="0" Background="#161A1E" BorderThickness="0,0,0,1" BorderBrush="#292F33">
                        <DockPanel Margin="12,0" VerticalAlignment="Center" Height="36">
                            <materialDesign:PackIcon Kind="Counter" Foreground="{StaticResource CountColor}" VerticalAlignment="Center" Height="18" Width="18"/>
                            <TextBlock Text="成交笔数榜" FontWeight="Bold" Margin="8,0,0,0" VerticalAlignment="Center" FontSize="14" Foreground="{StaticResource PrimaryTextColor}"/>
                        </DockPanel>
                    </Border>

                    <!-- 表头行 -->
                    <Grid Grid.Row="1" Margin="8,8,8,0" Height="24">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="30"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="#" Foreground="{StaticResource SecondaryTextColor}" FontSize="12" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="1" Text="交易对" Foreground="{StaticResource SecondaryTextColor}" FontSize="12" VerticalAlignment="Center" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="2" Text="价格" Margin="8,0" HorizontalAlignment="Right" VerticalAlignment="Center" Foreground="{StaticResource SecondaryTextColor}" FontSize="12" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="3" Text="成交笔数(M)" Margin="32,0,0,0" HorizontalAlignment="Right" VerticalAlignment="Center" Foreground="{StaticResource SecondaryTextColor}" FontSize="12" FontWeight="SemiBold"/>
                    </Grid>

                    <ListView Grid.Row="2" x:Name="CountRankingList" ItemsSource="{Binding CountRankingList}"
                              Background="Transparent" BorderThickness="0" SelectionChanged="RankingList_SelectionChanged"
                              ScrollViewer.VerticalScrollBarVisibility="Hidden" Padding="0"
                              VirtualizingPanel.IsVirtualizing="True"
                              VirtualizingPanel.VirtualizationMode="Recycling"
                              VirtualizingPanel.CacheLengthUnit="Page"
                              VirtualizingPanel.CacheLength="2,2"
                              VirtualizingPanel.ScrollUnit="Pixel"
                              ScrollViewer.IsDeferredScrollingEnabled="True"
                              ScrollViewer.CanContentScroll="True"
                              Focusable="True"
                              KeyboardNavigation.DirectionalNavigation="None"
                              KeyboardNavigation.TabNavigation="None"
                              local:ListViewHelper.ListViewName="CountRankingList"
                              Loaded="ListView_Loaded">
                        <ListView.CacheMode>
                            <BitmapCache EnableClearType="True" RenderAtScale="1.0" SnapsToDevicePixels="True" />
                        </ListView.CacheMode>
                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem" BasedOn="{StaticResource PurpleRankingItemStyle}"/>
                        </ListView.ItemContainerStyle>
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Grid RenderOptions.CachingHint="Cache" RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True">
                                    <Grid.CacheMode>
                                        <BitmapCache EnableClearType="True" RenderAtScale="1.0" SnapsToDevicePixels="True" />
                                    </Grid.CacheMode>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="30"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBlock Grid.Column="0" Text="{Binding Rank, StringFormat={}\{0:D2\}}"
                                               Foreground="{StaticResource SecondaryTextColor}" FontSize="12" VerticalAlignment="Center"
                                               RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True" />
                                    <TextBlock Grid.Column="1" Text="{Binding Symbol}"
                                               FontWeight="SemiBold" FontSize="13" VerticalAlignment="Center" Foreground="{StaticResource PrimaryTextColor}"
                                               RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True" />
                                    <TextBlock Grid.Column="2" Text="{Binding Price, StringFormat=N3}"
                                               Margin="8,0" HorizontalAlignment="Right" VerticalAlignment="Center" Foreground="{StaticResource SecondaryTextColor}"
                                               RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True" />
                                    <TextBlock Grid.Column="3" Text="{Binding Value, StringFormat={}{0:N2}M}"
                                               Margin="16,0,0,0" Foreground="{StaticResource CountColor}" HorizontalAlignment="Right" FontWeight="SemiBold" VerticalAlignment="Center"
                                               RenderOptions.ClearTypeHint="Enabled" UseLayoutRounding="True" />
                                </Grid>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                        <ListView.ItemsPanel>
                            <ItemsPanelTemplate>
                                <VirtualizingStackPanel VirtualizingPanel.IsVirtualizing="True"
                                                       VirtualizingPanel.VirtualizationMode="Recycling"
                                                       VirtualizingPanel.CacheLengthUnit="Page"
                                                       VirtualizingPanel.CacheLength="2,2"
                                                       RenderOptions.CachingHint="Cache" />
                            </ItemsPanelTemplate>
                        </ListView.ItemsPanel>
                    </ListView>
                </Grid>
            </Border>
        </Grid>

        <!-- 底部状态栏 -->
        <Border Grid.Row="2" Background="{DynamicResource WindowBackgroundBrush}" Height="36" BorderThickness="0,1,0,0" BorderBrush="{DynamicResource BorderBrush}">
            <DockPanel>
                <!-- 左侧时间信息 -->
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="16,0" DockPanel.Dock="Left">
                    <!-- 数据更新时间 -->
                    <materialDesign:PackIcon Kind="ClockOutline" VerticalAlignment="Center" Margin="0,0,6,0" Foreground="#F0B90B" Height="14" Width="14"/>
                    <TextBlock Text="数据更新:" VerticalAlignment="Center" FontSize="12" Foreground="{StaticResource SecondaryTextColor}"/>
                    <TextBlock Text="{Binding LastUpdateTime, StringFormat=HH:mm:ss}" VerticalAlignment="Center" FontSize="12" Margin="4,0,24,0" Foreground="{StaticResource PrimaryTextColor}"/>

                    <!-- WebSocket连接持续时间 -->
                    <materialDesign:PackIcon Kind="Connection" VerticalAlignment="Center" Margin="0,0,6,0" Foreground="#0ECB81" Height="14" Width="14"/>
                    <TextBlock Text="连接时长:" VerticalAlignment="Center" FontSize="12" Foreground="{StaticResource SecondaryTextColor}"/>
                    <TextBlock x:Name="ConnectionDurationText" Text="{Binding ConnectionDuration, StringFormat=hh\\:mm\\:ss}" VerticalAlignment="Center" FontSize="12" Margin="4,0,24,0" Foreground="{StaticResource PrimaryTextColor}"/>

                    <!-- 24小时重连剩余时间 -->
                    <materialDesign:PackIcon Kind="TimerOutline" VerticalAlignment="Center" Margin="0,0,6,0" Foreground="#F7931A" Height="14" Width="14"/>
                    <TextBlock Text="重连倒计时:" VerticalAlignment="Center" FontSize="12" Foreground="{StaticResource SecondaryTextColor}"/>
                    <TextBlock x:Name="RemainingReconnectTimeText" Text="{Binding RemainingReconnectTime, StringFormat=hh\\:mm\\:ss}" VerticalAlignment="Center" FontSize="12" Margin="4,0,0,0" Foreground="{StaticResource PrimaryTextColor}"/>
                </StackPanel>

                <!-- 右侧涨跌比例统计 -->
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="16,0" DockPanel.Dock="Right">
                    <!-- 涨跌比例图标 -->
                    <materialDesign:PackIcon Kind="TrendingUp" VerticalAlignment="Center" Margin="0,0,6,0" Foreground="{StaticResource GreenColor}" Height="14" Width="14"/>
                    <TextBlock Text="涨跌比例:" VerticalAlignment="Center" FontSize="12" Foreground="{StaticResource SecondaryTextColor}"/>

                    <!-- 上涨比例 -->
                    <TextBlock Text="{Binding RisingPercentage, StringFormat={}▲{0:F0}}" VerticalAlignment="Center" FontSize="12" Margin="4,0,0,0"
                               Foreground="{StaticResource GreenColor}" FontWeight="SemiBold"/>

                    <!-- 分隔符 -->
                    <TextBlock Text=" " VerticalAlignment="Center" FontSize="12" Margin="2,0" Foreground="{StaticResource SecondaryTextColor}"/>

                    <!-- 下跌比例 -->
                    <TextBlock Text="{Binding FallingPercentage, StringFormat={}▼{0:F0}}" VerticalAlignment="Center" FontSize="12"
                               Foreground="{StaticResource RedColor}" FontWeight="SemiBold"/>

                    <!-- 总数显示 -->
                    <TextBlock Text="{Binding TotalSymbolCount, StringFormat={}({0}个)}" VerticalAlignment="Center" FontSize="11" Margin="8,0,0,0"
                               Foreground="{StaticResource SecondaryTextColor}"/>
                </StackPanel>
            </DockPanel>
        </Border>
    </Grid>
</Window>
