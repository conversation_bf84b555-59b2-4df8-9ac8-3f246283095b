using System;

namespace SimpleBinanceRanking.Services
{
    /// <summary>
    /// 日志服务，用于管理应用程序的日志输出
    /// </summary>
    public static class LoggingService
    {
        /// <summary>
        /// 是否启用调试日志输出
        /// </summary>
        public static bool IsDebugEnabled { get; set; } = false;

        /// <summary>
        /// 是否启用信息日志输出
        /// </summary>
        public static bool IsInfoEnabled { get; set; } = true;

        /// <summary>
        /// 是否启用错误日志输出
        /// </summary>
        public static bool IsErrorEnabled { get; set; } = true;

        /// <summary>
        /// 输出调试日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public static void LogDebug(string message)
        {
            if (IsDebugEnabled)
            {
                Console.WriteLine($"[DEBUG] {DateTime.Now:HH:mm:ss.fff} {message}");
            }
        }

        /// <summary>
        /// 输出信息日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public static void LogInfo(string message)
        {
            if (IsInfoEnabled)
            {
                Console.WriteLine($"[INFO] {DateTime.Now:HH:mm:ss.fff} {message}");
            }
        }

        /// <summary>
        /// 输出错误日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public static void LogError(string message)
        {
            if (IsErrorEnabled)
            {
                Console.WriteLine($"[ERROR] {DateTime.Now:HH:mm:ss.fff} {message}");
            }
        }

        /// <summary>
        /// 输出错误日志（带异常信息）
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常对象</param>
        public static void LogError(string message, Exception exception)
        {
            if (IsErrorEnabled)
            {
                Console.WriteLine($"[ERROR] {DateTime.Now:HH:mm:ss.fff} {message}: {exception.Message}");
                if (IsDebugEnabled)
                {
                    Console.WriteLine($"[ERROR] Stack Trace: {exception.StackTrace}");
                }
            }
        }
    }
} 