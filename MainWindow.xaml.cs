using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives; // 添加这个命名空间引用
using System.Windows.Input;
using System.Windows.Interop;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects; // 添加这个命名空间引用，用于DropShadowEffect
using System.Windows.Threading;
using System.Windows.Documents; // 添加这个命名空间，用于TextElement
using WinForms = System.Windows.Forms; // 添加这个命名空间，用于NotifyIcon
using SimpleBinanceRanking.Models;
using SimpleBinanceRanking.Services;
using SimpleBinanceRanking.ViewModels;

namespace SimpleBinanceRanking;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    #region Windows API 声明

    [DllImport("user32.dll")]
    private static extern IntPtr GetForegroundWindow();

    [DllImport("user32.dll")]
    private static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint lpdwProcessId);

    [DllImport("user32.dll")]
    private static extern IntPtr GetKeyboardLayout(uint idThread);

    [DllImport("user32.dll")]
    private static extern bool ActivateKeyboardLayout(IntPtr hkl, uint flags);

    [DllImport("user32.dll")]
    private static extern int GetKeyboardLayoutList(int nBuff, IntPtr[] lpList);

    [DllImport("user32.dll")]
    private static extern IntPtr LoadKeyboardLayout(string pwszKLID, uint flags);

    // Caps Lock 控制相关API
    [DllImport("user32.dll")]
    private static extern short GetKeyState(int nVirtKey);

    [DllImport("user32.dll")]
    private static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, UIntPtr dwExtraInfo);

    private const uint KLF_ACTIVATE = 0x00000001;
    private const string ENGLISH_US_LAYOUT = "00000409"; // 美式英语键盘布局

    // 虚拟键码常量
    private const int VK_CAPITAL = 0x14; // Caps Lock键
    private const uint KEYEVENTF_KEYUP = 0x0002; // 按键释放标志

    #endregion

    private readonly MarketViewModel _marketViewModel;

    #region 窗口状态和托盘相关字段

    private bool _isReallyClosing = false;
    private WinForms.NotifyIcon? _notifyIcon;

    #endregion

    public MainWindow(MarketViewModel marketViewModel)
    {
        // 启用GPU加速
        EnableGpuAcceleration();

        InitializeComponent();

        _marketViewModel = marketViewModel;

        // 设置数据上下文
        DataContext = _marketViewModel;

        // 窗口加载完成后初始化数据
        Loaded += MainWindow_Loaded;

        // 订阅系统主题变化事件
        App.SystemThemeChanged += App_SystemThemeChanged;

        // 注册CompositionTarget.Rendering事件，用于监控渲染性能
        CompositionTarget.Rendering += CompositionTarget_Rendering;

        // 设置UI线程优先级
        Dispatcher.Thread.Priority = System.Threading.ThreadPriority.Highest;

        // 初始化系统托盘
        InitializeSystemTray();
    }

    /// <summary>
    /// 处理系统主题变化事件
    /// </summary>
    private void App_SystemThemeChanged(object? sender, bool isDarkMode)
    {
        // 由于此事件可能在非UI线程触发，使用Dispatcher确保在UI线程上更新
        Dispatcher.Invoke(() =>
        {
            // 更新窗口整体主题
            UpdateWindowForTheme(isDarkMode);

            // 更新窗口标题栏颜色
            Services.WindowThemeService.ApplyThemeToTitleBar(this, isDarkMode);

            Console.WriteLine($"主题已更新到: {(isDarkMode ? "深色模式" : "浅色模式")}");
        });
    }

    /// <summary>
    /// 窗口加载事件，支持跟随系统主题
    /// </summary>
    private void Window_Loaded(object sender, RoutedEventArgs e)
    {
        // 应用当前系统主题
        UpdateWindowForTheme(App.IsDarkMode);

        // 将系统主题应用到窗口标题栏
        Services.WindowThemeService.ApplyThemeToTitleBar(this, App.IsDarkMode);
    }

    /// <summary>
    /// 根据系统主题更新窗口样式
    /// </summary>
    /// <param name="isDarkMode">是否为深色模式</param>
    private void UpdateWindowForTheme(bool isDarkMode)
    {
        try
        {
            // 更新窗口标题栏颜色以跟随系统主题
            // 注意：这会应用动态资源，资源在App.xaml中已定义
            Console.WriteLine($"应用{(isDarkMode ? "深色" : "浅色")}主题到窗口");

            // 更新ListView的背景和前景色
            var cardBackgroundBrush = (SolidColorBrush)System.Windows.Application.Current.Resources["CardBackgroundColor"];
            var borderBrush = (SolidColorBrush)System.Windows.Application.Current.Resources["BorderBrush"];

            // 如果有其他需要根据主题特殊处理的UI元素，可以在这里更新
        }
        catch (Exception ex)
        {
            Console.WriteLine($"应用主题到窗口时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 启用GPU加速
    /// </summary>
    private void EnableGpuAcceleration()
    {
        try
        {
            // 检测系统渲染能力
            var renderTier = (RenderCapability.Tier >> 16);

            // 启用硬件加速
            RenderOptions.ProcessRenderMode = System.Windows.Interop.RenderMode.Default;
            Console.WriteLine($"MainWindow: 启用硬件加速渲染，渲染层级: Tier {renderTier}");

            // 启用布局舍入
            UseLayoutRounding = true;

            // 启用像素对齐
            SnapsToDevicePixels = true;

            // 设置缓存模式 - 使用更高级的缓存设置
            CacheMode = new BitmapCache
            {
                EnableClearType = true,
                SnapsToDevicePixels = true,
                RenderAtScale = 1.0
            };

            // 设置渲染选项
            RenderOptions.SetBitmapScalingMode(this, BitmapScalingMode.HighQuality);
            RenderOptions.SetClearTypeHint(this, ClearTypeHint.Enabled);
            RenderOptions.SetEdgeMode(this, EdgeMode.Aliased);
            RenderOptions.SetCachingHint(this, CachingHint.Cache);

            // 设置文本渲染选项
            TextOptions.SetTextFormattingMode(this, TextFormattingMode.Display);
            TextOptions.SetTextRenderingMode(this, TextRenderingMode.ClearType);

            Console.WriteLine($"已在MainWindow中启用GPU加速，渲染层级: Tier {renderTier}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"启用GPU加速时发生错误: {ex.Message}");
        }
    }

    // 用于监控帧率的变量
    private int _frameCount = 0;
    private DateTime _lastFpsUpdate = DateTime.Now;
    private readonly int _fpsLogInterval = 30; // 帧率日志输出间隔（秒）
    private float _lastFps = 0;
    private readonly bool _enableFpsLogging = false; // 默认关闭帧率日志输出

    /// <summary>
    /// 监控渲染性能
    /// </summary>
    private void CompositionTarget_Rendering(object? sender, EventArgs e)
    {
        if (!_enableFpsLogging) return;

        _frameCount++;

        // 每30秒计算一次帧率并输出日志
        var now = DateTime.Now;
        if ((now - _lastFpsUpdate).TotalSeconds >= _fpsLogInterval)
        {
            // 计算帧率
            _lastFps = (float)(_frameCount / (now - _lastFpsUpdate).TotalSeconds);
            LoggingService.LogInfo($"当前帧率: {_lastFps:F1} FPS");

            // 重置计数器
            _frameCount = 0;
            _lastFpsUpdate = now;
        }
    }

    private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
    {
        try
        {
            // 应用系统默认字体
            ApplySystemFont();

            // 添加ConnectionState属性变化的监听
            _marketViewModel.PropertyChanged += (s, args) =>
            {
                if (args.PropertyName == nameof(MarketViewModel.ConnectionState))
                {
                    // 在UI线程上更新连接状态指示器
                    Dispatcher.InvokeAsync(() => UpdateConnectionStatusIndicator(), DispatcherPriority.Render);
                }
            };

            // 初始化连接状态指示器
            UpdateConnectionStatusIndicator();

            // 初始化市场数据
            await _marketViewModel.Initialize();

            Console.WriteLine("窗口加载完成，准备设置焦点");

            // 确保窗口获取焦点，以便接收键盘事件
            await Dispatcher.InvokeAsync(() =>
            {
                // 构建符号到项目的映射
                BuildSymbolToItemMap();

                // 尝试多种方式设置焦点
                MainGrid.Focus();
                Console.WriteLine($"MainGrid是否有焦点: {MainGrid.IsFocused}");

                Focus();
                Console.WriteLine($"窗口是否有焦点: {IsFocused}");

                // 设置搜索框焦点
                SetSearchBoxFocus();

                // 添加键盘事件处理器
                KeyDown += MainWindow_KeyDown;
                Console.WriteLine("已添加额外的键盘事件处理器");

                // 更新断开连接按钮状态和连接状态指示器
                UpdateDisconnectButtonState();
                UpdateConnectionStatusIndicator();
            }, DispatcherPriority.ApplicationIdle);
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"初始化数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            UpdateConnectionStatusIndicator();
        }
    }

    /// <summary>
    /// 应用系统默认字体（微软雅黑）
    /// </summary>
    private void ApplySystemFont()
    {
        try
        {
            Console.WriteLine("应用系统默认字体（微软雅黑）到界面元素...");

            // 创建微软雅黑字体族
            var microsoftYaHeiFont = new System.Windows.Media.FontFamily("Microsoft YaHei");

            // 应用字体到窗口
            this.FontFamily = microsoftYaHeiFont;

            // 应用字体到主要UI元素
            if (MainGrid != null)
            {
                TextElement.SetFontFamily(MainGrid, microsoftYaHeiFont);
            }

            // 应用字体到所有排行榜
            if (PriceChangePercentRankingList != null)
                PriceChangePercentRankingList.FontFamily = microsoftYaHeiFont;

            if (PriceChangePercentDescRankingList != null)
                PriceChangePercentDescRankingList.FontFamily = microsoftYaHeiFont;

            if (PriceVolatilityRankingList != null)
                PriceVolatilityRankingList.FontFamily = microsoftYaHeiFont;

            if (VolumeRankingList != null)
                VolumeRankingList.FontFamily = microsoftYaHeiFont;

            if (CountRankingList != null)
                CountRankingList.FontFamily = microsoftYaHeiFont;

            // 应用字体到状态栏文本
            if (ConnectionStatusText != null)
                ConnectionStatusText.FontFamily = microsoftYaHeiFont;

            if (DisconnectButton != null)
                DisconnectButton.FontFamily = microsoftYaHeiFont;

            // 应用字体到底部状态栏的新TextBlock
            if (ConnectionDurationText != null)
                ConnectionDurationText.FontFamily = microsoftYaHeiFont;

            if (RemainingReconnectTimeText != null)
                RemainingReconnectTimeText.FontFamily = microsoftYaHeiFont;

            Console.WriteLine("已应用系统默认字体（微软雅黑）");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"应用系统默认字体时发生错误: {ex.Message}");

            // 如果微软雅黑不可用，使用系统默认字体
            try
            {
                var systemFont = System.Windows.SystemFonts.MessageFontFamily;
                this.FontFamily = systemFont;
                Console.WriteLine($"已回退到系统默认字体: {systemFont.Source}");
            }
            catch (Exception fallbackEx)
            {
                Console.WriteLine($"设置回退字体时发生错误: {fallbackEx.Message}");
            }
        }
    }

    /// <summary>
    /// 构建符号到项目的映射
    /// </summary>
    private void BuildSymbolToItemMap()
    {
        try
        {
            Console.WriteLine("开始构建符号到项目的映射");
            _symbolToItemMap.Clear();

            // 处理所有ListView
            BuildMapForListView(PriceChangePercentRankingList);
            BuildMapForListView(PriceChangePercentDescRankingList);

            // 如果有其他ListView，也添加到这里

            Console.WriteLine($"符号到项目的映射构建完成，共 {_symbolToItemMap.Count} 个项目");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"构建符号到项目的映射时出错: {ex.Message}");
        }
    }

    /// <summary>
    /// 为指定的ListView构建映射
    /// </summary>
    private void BuildMapForListView(System.Windows.Controls.ListView listView)
    {
        if (listView == null || listView.Items.Count == 0)
            return;

        Console.WriteLine($"处理ListView: {listView.Name}, 项目数: {listView.Items.Count}");

        // 等待ListView完成项目容器的生成
        listView.UpdateLayout();

        for (int i = 0; i < listView.Items.Count; i++)
        {
            var item = listView.ItemContainerGenerator.ContainerFromIndex(i) as System.Windows.Controls.ListViewItem;
            if (item != null && item.DataContext is RankingData rankingData)
            {
                string symbol = rankingData.Symbol;
                if (!string.IsNullOrEmpty(symbol))
                {
                    _symbolToItemMap[symbol] = (listView, i, item);
                    // 已移除映射日志输出以提高性能
                }
            }
        }
    }

    /// <summary>
    /// 额外的键盘事件处理器
    /// </summary>
    private void MainWindow_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
    {
        Console.WriteLine($"额外键盘事件触发: {e.Key}");

        // 如果事件未被处理，调用我们的OnKeyDown方法
        if (!e.Handled && (e.Key == Key.Up || e.Key == Key.Down))
        {
            Console.WriteLine("未找到组合字体，将使用系统默认字体");
            return;
        }

        try
        {
            _isKeyboardNavigating = true;

            // 获取当前悬停的项目
            if (_currentHoverItem != null)
            {
                // 获取ListView名称和索引
                string listViewName = ListViewHelper.GetListViewName(_currentHoverItem);
                int currentIndex = ListViewHelper.GetListViewIndex(_currentHoverItem);

                Console.WriteLine($"当前项目: {_lastHoveredSymbol}, ListView: {listViewName}, 索引: {currentIndex}");

                // 根据名称获取ListView
                System.Windows.Controls.ListView? listView = null;
                switch (listViewName)
                {
                    case "PriceChangePercentRankingList":
                        listView = PriceChangePercentRankingList;
                        break;
                    case "PriceChangePercentDescRankingList":
                        listView = PriceChangePercentDescRankingList;
                        break;
                    case "PriceVolatilityRankingList":
                        listView = PriceVolatilityRankingList;
                        break;
                    case "VolumeRankingList":
                        listView = VolumeRankingList;
                        break;
                    case "CountRankingList":
                        listView = CountRankingList;
                        break;
                }

                if (listView != null)
                {
                    // 计算新的索引
                    int newIndex = currentIndex + (e.Key == Key.Up ? -1 : 1);
                    Console.WriteLine($"计算新索引: {newIndex}, 列表项数: {listView.Items.Count}");

                    // 确保索引在有效范围内
                    if (newIndex >= 0 && newIndex < listView.Items.Count)
                    {
                        // 获取新的项目
                        var newItem = listView.ItemContainerGenerator.ContainerFromIndex(newIndex) as System.Windows.Controls.ListViewItem;
                        Console.WriteLine($"新项目: {(newItem != null ? "找到" : "未找到")}");

                        if (newItem != null && newItem.DataContext is RankingData newRankingData)
                        {
                            string newSymbol = newRankingData.Symbol;
                            Console.WriteLine($"新符号: {newSymbol}");

                            // 更新当前索引
                            _currentHoverIndex = newIndex;

                            // 更新高亮状态
                            _lastHoveredSymbol = newSymbol;
                            _marketViewModel.SetHighlight(newSymbol);

                            // 确保项目可见
                            newItem.BringIntoView();

                            // 标记事件已处理
                            e.Handled = true;
                            Console.WriteLine("导航成功");
                        }
                        else
                        {
                            Console.WriteLine($"无法获取项目或DataContext: Item={newItem}, DataContext={(newItem?.DataContext?.ToString() ?? "null")}");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"索引超出范围: {newIndex}");
                    }
                }
                else
                {
                    Console.WriteLine($"无法获取ListView: {listViewName}");
                }
            }
            else if (_currentHoverListView != null && _currentHoverIndex >= 0)
            {
                // 使用当前悬停的ListView和索引
                Console.WriteLine($"使用当前悬停的ListView: {_currentHoverListView.Name}, 索引: {_currentHoverIndex}");

                // 计算新的索引
                int newIndex = _currentHoverIndex + (e.Key == Key.Up ? -1 : 1);
                Console.WriteLine($"计算新索引: {newIndex}, 列表项数: {_currentHoverListView.Items.Count}");

                // 确保索引在有效范围内
                if (newIndex >= 0 && newIndex < _currentHoverListView.Items.Count)
                {
                    // 获取新的项目
                    var newItem = _currentHoverListView.ItemContainerGenerator.ContainerFromIndex(newIndex) as System.Windows.Controls.ListViewItem;
                    Console.WriteLine($"新项目: {(newItem != null ? "找到" : "未找到")}");

                    if (newItem != null && newItem.DataContext is RankingData newRankingData)
                    {
                        string newSymbol = newRankingData.Symbol;
                        Console.WriteLine($"新符号: {newSymbol}");

                        // 更新当前索引
                        _currentHoverIndex = newIndex;

                        // 更新高亮状态
                        _lastHoveredSymbol = newSymbol;
                        ApplyHighlightWithPriority();

                        // 确保项目可见
                        newItem.BringIntoView();

                        // 标记事件已处理
                        e.Handled = true;
                        Console.WriteLine("导航成功");
                    }
                    else
                    {
                        Console.WriteLine($"无法获取项目或DataContext: Item={newItem}, DataContext={(newItem?.DataContext?.ToString() ?? "null")}");
                    }
                }
                else
                {
                    Console.WriteLine($"索引超出范围: {newIndex}");
                }
            }
            else
            {
                Console.WriteLine("无法获取当前悬停的项目信息");

                // 尝试使用符号到项目的映射
                if (_symbolToItemMap.TryGetValue(_lastHoveredSymbol, out var itemInfo))
                {
                    var (listView, index, item) = itemInfo;
                    Console.WriteLine($"从映射中找到了当前悬停的交易对: {_lastHoveredSymbol}, ListView: {listView.Name}, 索引: {index}");

                    // 更新当前项目信息
                    _currentHoverListView = listView;
                    _currentHoverIndex = index;
                    _currentHoverItem = item;

                    // 下次按键时将使用这个信息
                }
                else
                {
                    Console.WriteLine($"在映射中找不到当前悬停的交易对: {_lastHoveredSymbol}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"键盘导航出错: {ex.Message}");
        }
        finally
        {
            _isKeyboardNavigating = false;
        }
    }

    /// <summary>
    /// 显示键盘导航提示
    /// </summary>
    private void ShowKeyboardNavigationTip()
    {
        // 创建一个提示窗口，告诉用户可以使用方向键导航
        var tipWindow = new Window
        {
            Title = "提示",
            Width = 300,
            Height = 150,
            WindowStartupLocation = WindowStartupLocation.CenterOwner,
            Owner = this,
            ResizeMode = ResizeMode.NoResize,
            WindowStyle = WindowStyle.ToolWindow,
            Background = new SolidColorBrush(System.Windows.Media.Color.FromRgb(27, 32, 38))
        };

        var panel = new StackPanel
        {
            Margin = new Thickness(20),
            VerticalAlignment = VerticalAlignment.Center
        };

        var textBlock = new TextBlock
        {
            Text = "现在您可以使用键盘上下方向键在排行榜中导航。\n\n鼠标悬停在任意交易对上，然后使用↑↓键切换。",
            TextWrapping = TextWrapping.Wrap,
            Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(234, 236, 239)),
            FontSize = 14,
            TextAlignment = TextAlignment.Center,
            Margin = new Thickness(0, 0, 0, 15)
        };

        var button = new System.Windows.Controls.Button
        {
            Content = "了解",
            Width = 80,
            Height = 30,
            HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
            Background = new SolidColorBrush(System.Windows.Media.Color.FromRgb(240, 185, 11)),
            Foreground = new SolidColorBrush(Colors.Black)
        };

        button.Click += (s, e) => tipWindow.Close();

        panel.Children.Add(textBlock);
        panel.Children.Add(button);
        tipWindow.Content = panel;

        tipWindow.Show();
    }

    /// <summary>
    /// 更新WebSocket连接状态指示器
    /// </summary>
    private void UpdateConnectionStatusIndicator()
    {
        string status = _marketViewModel.ConnectionState;
        string displayStatus = status;

        // 简化状态显示，移除倒计时信息
        if (status.Contains("已连接"))
        {
            displayStatus = "已连接";
            // 绿色 - 连接正常
            ConnectionStatusIndicator.Fill = new SolidColorBrush(System.Windows.Media.Color.FromRgb(14, 203, 129)); // 绿色
            ((DropShadowEffect)ConnectionStatusIndicator.Effect).Color = System.Windows.Media.Color.FromRgb(14, 203, 129);
        }
        else if (status.Contains("正在连接") || status.Contains("正在刷新") || status.Contains("重新连接中"))
        {
            displayStatus = status; // 保持原状态文本
            // 黄色 - 正在连接或重试
            ConnectionStatusIndicator.Fill = new SolidColorBrush(System.Windows.Media.Color.FromRgb(240, 185, 11)); // 黄色
            ((DropShadowEffect)ConnectionStatusIndicator.Effect).Color = System.Windows.Media.Color.FromRgb(240, 185, 11);
        }
        else
        {
            // 红色 - 连接失败或已断开
            ConnectionStatusIndicator.Fill = new SolidColorBrush(System.Windows.Media.Color.FromRgb(246, 70, 93)); // 红色
            ((DropShadowEffect)ConnectionStatusIndicator.Effect).Color = System.Windows.Media.Color.FromRgb(246, 70, 93);
        }

        // 更新状态文本
        ConnectionStatusText.Text = displayStatus;
    }

    private async void DisconnectButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (_marketViewModel.IsDisconnected)
            {
                // 如果已断开连接，则重新连接
                DisconnectButton.Content = "正在连接...";
                DisconnectButton.IsEnabled = false;
                _marketViewModel.ConnectionState = "重新连接中";
                UpdateConnectionStatusIndicator();

                // 添加更多日志以便调试
                Console.WriteLine("开始重新初始化连接...");

                try
                {
                    // 确保之前的连接已完全断开
                    _marketViewModel.StopConnection();

                    // 等待一小段时间确保资源释放
                    await Task.Delay(500);

                    // 重新初始化
                    await _marketViewModel.Initialize();
                    Console.WriteLine("重新初始化完成");
                }
                catch (Exception initEx)
                {
                    Console.WriteLine($"重新初始化过程中出错: {initEx.Message}\n{initEx.StackTrace}");
                    throw; // 重新抛出异常以便被外层catch捕获
                }

                // 更新按钮状态和连接状态指示器
                UpdateDisconnectButtonState();
                UpdateConnectionStatusIndicator();
                DisconnectButton.IsEnabled = true;
            }
            else
            {
                // 如果已连接，则断开连接
                Console.WriteLine("断开连接...");
                _marketViewModel.StopConnection();

                // 更新按钮状态和连接状态指示器
                UpdateDisconnectButtonState();
                UpdateConnectionStatusIndicator();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"断开/重连操作失败: {ex.Message}\n{ex.StackTrace}");
            System.Windows.MessageBox.Show($"操作失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            DisconnectButton.IsEnabled = true;
            UpdateConnectionStatusIndicator();
        }
    }

    /// <summary>
    /// 更新断开连接按钮状态
    /// </summary>
    private void UpdateDisconnectButtonState()
    {
        if (_marketViewModel.IsDisconnected)
        {
            DisconnectButton.Content = "重新连接";
            DisconnectButton.Background = new SolidColorBrush(System.Windows.Media.Color.FromRgb(240, 185, 11)); // 黄色
            DisconnectButton.BorderBrush = new SolidColorBrush(System.Windows.Media.Color.FromRgb(240, 185, 11));
            DisconnectButton.Foreground = new SolidColorBrush(Colors.Black);
        }
        else
        {
            DisconnectButton.Content = "断开连接";
            DisconnectButton.Background = new SolidColorBrush(System.Windows.Media.Color.FromRgb(44, 44, 44)); // 灰色
            DisconnectButton.BorderBrush = new SolidColorBrush(System.Windows.Media.Color.FromRgb(44, 44, 44));
            DisconnectButton.Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(234, 236, 239)); // 柔和白色
        }
    }

    private void RankingList_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (sender is System.Windows.Controls.ListView listView && listView.SelectedItem is RankingData selectedItem)
        {
            // 设置选中的交易对
            _marketViewModel.SelectedSymbol = selectedItem.Symbol;

            // 打开币安交易页面
            OpenBinanceUrl(selectedItem.Symbol);

            // 清除选中状态
            listView.SelectedItem = null;
        }
    }

    // 用于记录上次悬停的交易对
    private string _lastHoveredSymbol = string.Empty;

    // 用于跟踪当前悬停的ListView和项目索引
    private System.Windows.Controls.ListView? _currentHoverListView = null;
    private int _currentHoverIndex = -1;

    // 用于跟踪当前悬停的ListViewItem
    private System.Windows.Controls.ListViewItem? _currentHoverItem = null;

    // 用于跟踪所有ListView中的项目
    private Dictionary<string, (System.Windows.Controls.ListView ListView, int Index, System.Windows.Controls.ListViewItem Item)> _symbolToItemMap = new Dictionary<string, (System.Windows.Controls.ListView, int, System.Windows.Controls.ListViewItem)>();

    // 输入框高亮功能相关字段
    private string _searchInputSymbol = string.Empty;
    private bool _isMouseHovering = false;

    /// <summary>
    /// ListView加载完成事件处理
    /// </summary>
    private void ListView_Loaded(object sender, RoutedEventArgs e)
    {
        if (sender is System.Windows.Controls.ListView listView)
        {
            string listViewName = ListViewHelper.GetListViewName(listView);
            Console.WriteLine($"ListView加载完成: {listViewName}");

            // 注册ItemContainerGenerator.StatusChanged事件
            listView.ItemContainerGenerator.StatusChanged += (s, args) => ListView_StatusChanged(listView, s as ItemContainerGenerator);

            // 如果容器已经生成，直接处理
            if (listView.ItemContainerGenerator.Status == GeneratorStatus.ContainersGenerated)
            {
                ListView_StatusChanged(listView, listView.ItemContainerGenerator);
            }
        }
    }

    /// <summary>
    /// ListView状态改变事件处理
    /// </summary>
    private void ListView_StatusChanged(System.Windows.Controls.ListView listView, ItemContainerGenerator? generator)
    {
        if (generator == null || generator.Status != GeneratorStatus.ContainersGenerated)
            return;

        string listViewName = ListViewHelper.GetListViewName(listView);
        Console.WriteLine($"ListView容器生成完成: {listViewName}");

        // 为每个ListViewItem设置附加属性
        for (int i = 0; i < listView.Items.Count; i++)
        {
            var item = listView.ItemContainerGenerator.ContainerFromIndex(i) as System.Windows.Controls.ListViewItem;
            if (item != null)
            {
                ListViewHelper.SetListViewName(item, listViewName);
                ListViewHelper.SetListViewIndex(item, i);

                if (item.DataContext is RankingData rankingData && !string.IsNullOrEmpty(rankingData.Symbol))
                {
                    string symbol = rankingData.Symbol;
                    // 更新符号到项目的映射
                    _symbolToItemMap[symbol] = (listView, i, item);
                    // 已移除映射日志输出以提高性能
                }
            }
        }
    }

    /// <summary>
    /// 鼠标进入ListViewItem事件处理
    /// </summary>
    private void ListViewItem_MouseEnter(object sender, System.Windows.Input.MouseEventArgs e)
    {
        if (sender is System.Windows.Controls.ListViewItem item && item.DataContext is RankingData rankingData)
        {
            string symbol = rankingData.Symbol;
            Console.WriteLine($"鼠标进入: {symbol}");

            // 设置鼠标悬停状态
            _isMouseHovering = true;

            // 如果与上次悬停的交易对相同，不做处理
            if (symbol == _lastHoveredSymbol)
            {
                Console.WriteLine("与上次悬停相同，不处理");
                return;
            }

            // 记录当前悬停的项目
            _currentHoverItem = item;

            // 获取ListView名称和索引
            string listViewName = ListViewHelper.GetListViewName(item);
            int index = ListViewHelper.GetListViewIndex(item);

            if (!string.IsNullOrEmpty(listViewName))
            {
                // 根据名称获取ListView
                System.Windows.Controls.ListView? listView = null;
                switch (listViewName)
                {
                    case "PriceChangePercentRankingList":
                        listView = PriceChangePercentRankingList;
                        break;
                    case "PriceChangePercentDescRankingList":
                        listView = PriceChangePercentDescRankingList;
                        break;
                    case "PriceVolatilityRankingList":
                        listView = PriceVolatilityRankingList;
                        break;
                    case "VolumeRankingList":
                        listView = VolumeRankingList;
                        break;
                    case "CountRankingList":
                        listView = CountRankingList;
                        break;
                }

                if (listView != null)
                {
                    _currentHoverListView = listView;
                    _currentHoverIndex = index;
                    Console.WriteLine($"设置当前ListView: {listViewName}, 索引: {index}");

                    // 确保窗口有焦点，以便接收键盘事件
                    if (!IsFocused)
                    {
                        Focus();
                        Console.WriteLine("设置窗口焦点");
                    }

                    // 更新符号到项目的映射
                    if (!_symbolToItemMap.ContainsKey(symbol))
                    {
                        _symbolToItemMap[symbol] = (listView, index, item);
                        // 已移除映射日志输出以提高性能
                    }
                }
            }
            else
            {
                Console.WriteLine($"无法获取ListView名称");
            }

            // 更新鼠标悬停的符号并应用优先级高亮逻辑
            _lastHoveredSymbol = symbol;
            ApplyHighlightWithPriority();
            Console.WriteLine($"设置高亮: {symbol}");
        }
    }

    /// <summary>
    /// 鼠标离开ListViewItem事件处理
    /// </summary>
    private void ListViewItem_MouseLeave(object sender, System.Windows.Input.MouseEventArgs e)
    {
        if (sender is System.Windows.Controls.ListViewItem item && item.DataContext is RankingData rankingData)
        {
            string symbol = rankingData.Symbol;

            // 清除鼠标悬停状态
            _isMouseHovering = false;

            // 只有当离开的是当前高亮的项目时，才处理高亮逻辑
            // 但如果是通过键盘导航离开的，不清除高亮
            if (symbol == _lastHoveredSymbol && !_isKeyboardNavigating)
            {
                // 清除鼠标悬停相关状态
                _lastHoveredSymbol = string.Empty;
                _currentHoverListView = null;
                _currentHoverIndex = -1;
                _currentHoverItem = null;

                // 应用优先级高亮逻辑
                ApplyHighlightWithPriority();
            }
        }
    }

    // 标记是否正在使用键盘导航
    private bool _isKeyboardNavigating = false;

    /// <summary>
    /// 窗口键盘按键事件处理
    /// </summary>
    protected override void OnKeyDown(System.Windows.Input.KeyEventArgs e)
    {
        base.OnKeyDown(e);

        // 添加调试输出
        Console.WriteLine($"键盘事件触发: {e.Key}, 是否有焦点: {IsFocused}, 源: {e.Source?.GetType().Name}");

        // 处理F5键 - 用于断开连接/重新连接功能
        if (e.Key == Key.F5)
        {
            Console.WriteLine("检测到F5键，触发断开连接/重新连接功能");
            DisconnectButton_Click(DisconnectButton, new RoutedEventArgs());
            e.Handled = true;
            return;
        }

        // 只处理上下方向键
        if (e.Key != Key.Up && e.Key != Key.Down)
            return;

        // 添加调试输出
        Console.WriteLine($"当前悬停ListView: {_currentHoverListView?.Name ?? "无"}, 索引: {_currentHoverIndex}");

        // 如果当前没有悬停的ListView，不处理
        if (_currentHoverListView == null || _currentHoverIndex < 0)
        {
            Console.WriteLine("没有当前悬停的ListView或索引无效，尝试使用第一个ListView");

            // 尝试使用第一个ListView
            _currentHoverListView = PriceChangePercentRankingList;
            _currentHoverIndex = 0;

            if (_currentHoverListView.Items.Count == 0)
            {
                Console.WriteLine("ListView为空，无法导航");
                return;
            }
        }

        try
        {
            _isKeyboardNavigating = true;

            // 计算新的索引
            int newIndex = _currentHoverIndex + (e.Key == Key.Up ? -1 : 1);
            Console.WriteLine($"计算新索引: {newIndex}, 列表项数: {_currentHoverListView.Items.Count}");

            // 确保索引在有效范围内
            if (newIndex >= 0 && newIndex < _currentHoverListView.Items.Count)
            {
                // 获取新的项目
                var newItem = _currentHoverListView.ItemContainerGenerator.ContainerFromIndex(newIndex) as System.Windows.Controls.ListViewItem;
                Console.WriteLine($"新项目: {(newItem != null ? "找到" : "未找到")}");

                if (newItem != null && newItem.DataContext is RankingData newRankingData)
                {
                    string newSymbol = newRankingData.Symbol;
                    Console.WriteLine($"新符号: {newSymbol}");

                    // 更新当前索引
                    _currentHoverIndex = newIndex;

                    // 更新高亮状态
                    _lastHoveredSymbol = newSymbol;
                    ApplyHighlightWithPriority();

                    // 确保项目可见
                    newItem.BringIntoView();

                    // 标记事件已处理
                    e.Handled = true;
                    Console.WriteLine("导航成功");
                }
                else
                {
                    Console.WriteLine($"无法获取项目或DataContext: Item={newItem}, DataContext={(newItem?.DataContext?.ToString() ?? "null")}");
                }
            }
            else
            {
                Console.WriteLine($"索引超出范围: {newIndex}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"键盘导航出错: {ex.Message}");
        }
        finally
        {
            _isKeyboardNavigating = false;
        }
    }

    private void OpenBinanceUrl(string symbol)
    {
        try
        {
            // 由于RankingData中已经移除了USDT后缀，这里需要手动添加回来
            string fullSymbol = $"{symbol}USDT";

            // 统一使用固定的Binance官方URL格式，不受配置文件影响
            // 配置文件中的URL只影响数据获取，不影响点击跳转的链接
            var url = $"https://www.binance.com/zh-CN/futures/{fullSymbol}?layout=pro&theme=dark";

            Console.WriteLine($"打开URL: {url}");

            Process.Start(new ProcessStartInfo
            {
                FileName = url,
                UseShellExecute = true
            });
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"打开浏览器失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        try
        {
            // 取消注册CompositionTarget.Rendering事件
            CompositionTarget.Rendering -= CompositionTarget_Rendering;

            // 关闭窗口时断开连接
            _marketViewModel.StopConnection();

            // 清理托盘图标
            if (_notifyIcon != null)
            {
                _notifyIcon.Visible = false;
                _notifyIcon.Dispose();
                _notifyIcon = null;
            }

            // 释放资源
            GC.Collect();
            GC.WaitForPendingFinalizers();

            Console.WriteLine("已释放所有资源");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"关闭窗口时发生错误: {ex.Message}");
        }
        finally
        {
            base.OnClosed(e);
        }
    }

    #region 输入框高亮功能

    /// <summary>
    /// 输入框文本变化事件处理
    /// </summary>
    private void SymbolSearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
    {
        if (sender is System.Windows.Controls.TextBox textBox)
        {
            string inputText = textBox.Text?.Trim().ToUpperInvariant() ?? string.Empty;

            // 更新搜索输入的符号
            _searchInputSymbol = inputText;

            // 应用高亮逻辑（考虑优先级）
            ApplyHighlightWithPriority();
        }
    }

    /// <summary>
    /// 强制切换到英文输入法
    /// </summary>
    private void ForceEnglishInputMethod()
    {
        try
        {
            // 获取当前窗口句柄
            var windowHandle = new WindowInteropHelper(this).Handle;
            if (windowHandle == IntPtr.Zero)
            {
                Console.WriteLine("无法获取窗口句柄");
                return;
            }

            // 获取当前线程ID
            uint currentThreadId = GetWindowThreadProcessId(windowHandle, out _);

            // 获取当前键盘布局
            IntPtr currentLayout = GetKeyboardLayout(currentThreadId);
            Console.WriteLine($"当前键盘布局: 0x{currentLayout.ToInt64():X}");

            // 尝试加载并激活英文键盘布局
            IntPtr englishLayout = LoadKeyboardLayout(ENGLISH_US_LAYOUT, KLF_ACTIVATE);
            if (englishLayout != IntPtr.Zero)
            {
                bool success = ActivateKeyboardLayout(englishLayout, KLF_ACTIVATE);
                Console.WriteLine($"切换到英文输入法: {(success ? "成功" : "失败")}");
            }
            else
            {
                Console.WriteLine("无法加载英文键盘布局");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"强制切换输入法时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 设置搜索框焦点
    /// </summary>
    private void SetSearchBoxFocus()
    {
        try
        {
            // 延迟设置焦点，确保UI完全加载
            Dispatcher.BeginInvoke(new Action(() =>
            {
                if (SymbolSearchTextBox != null)
                {
                    SymbolSearchTextBox.Focus();
                    Console.WriteLine($"搜索框是否获得焦点: {SymbolSearchTextBox.IsFocused}");

                    // 设置输入法为英文状态
                    InputMethod.SetPreferredImeState(SymbolSearchTextBox, InputMethodState.Off);

                    // 强制切换到英文输入法
                    ForceEnglishInputMethod();

                    Console.WriteLine("已设置输入法为英文状态");
                }
            }), DispatcherPriority.Input);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"设置搜索框焦点时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 输入框获得焦点事件处理
    /// </summary>
    private void SymbolSearchTextBox_GotFocus(object sender, RoutedEventArgs e)
    {
        try
        {
            // 设置输入法为英文状态
            if (sender is System.Windows.Controls.TextBox textBox)
            {
                InputMethod.SetPreferredImeState(textBox, InputMethodState.Off);

                // 强制切换到英文输入法
                ForceEnglishInputMethod();

                Console.WriteLine("输入框获得焦点，已设置输入法为英文状态");
            }

            // 输入框获得焦点时，如果有内容则应用高亮
            if (!string.IsNullOrEmpty(_searchInputSymbol))
            {
                ApplyHighlightWithPriority();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"输入框获得焦点处理时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 输入框失去焦点事件处理
    /// </summary>
    private void SymbolSearchTextBox_LostFocus(object sender, RoutedEventArgs e)
    {
        // 输入框失去焦点时，保持当前高亮状态不变
        // 高亮状态由优先级逻辑控制
    }

    /// <summary>
    /// 根据优先级应用高亮逻辑
    /// 优先级：鼠标悬停 > 输入框搜索
    /// </summary>
    private void ApplyHighlightWithPriority()
    {
        try
        {
            // 如果鼠标正在悬停，鼠标悬停优先
            if (_isMouseHovering && !string.IsNullOrEmpty(_lastHoveredSymbol))
            {
                _marketViewModel.SetHighlight(_lastHoveredSymbol);
                Console.WriteLine($"应用鼠标悬停高亮: {_lastHoveredSymbol}");
            }
            // 否则应用输入框搜索高亮
            else if (!string.IsNullOrEmpty(_searchInputSymbol))
            {
                _marketViewModel.SetHighlight(_searchInputSymbol);
                Console.WriteLine($"应用输入框搜索高亮: {_searchInputSymbol}");
            }
            // 都没有则清除高亮
            else
            {
                _marketViewModel.ClearHighlight();
                Console.WriteLine("清除所有高亮");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"应用高亮优先级时发生错误: {ex.Message}");
        }
    }

    #endregion

    #region 系统托盘和窗口管理功能

    /// <summary>
    /// 初始化系统托盘
    /// </summary>
    private void InitializeSystemTray()
    {
        try
        {
            // 创建托盘图标
            _notifyIcon = new WinForms.NotifyIcon();

            // 设置托盘图标（使用应用程序图标）
            _notifyIcon.Icon = LoadApplicationIcon();

            // 设置托盘提示文本
            _notifyIcon.Text = "BINANCE合约排行榜";

            // 设置托盘图标可见
            _notifyIcon.Visible = true;

            // 创建右键菜单
            var contextMenu = new WinForms.ContextMenuStrip();

            // 显示主窗口菜单项
            var showMenuItem = new WinForms.ToolStripMenuItem("显示主窗口");
            showMenuItem.Click += (s, e) => ShowMainWindow();
            contextMenu.Items.Add(showMenuItem);

            // 分隔线
            contextMenu.Items.Add(new WinForms.ToolStripSeparator());

            // 退出菜单项
            var exitMenuItem = new WinForms.ToolStripMenuItem("退出程序");
            exitMenuItem.Click += (s, e) => ExitApplication();
            contextMenu.Items.Add(exitMenuItem);

            // 设置右键菜单
            _notifyIcon.ContextMenuStrip = contextMenu;

            // 双击托盘图标显示主窗口
            _notifyIcon.DoubleClick += (s, e) => ShowMainWindow();

            Console.WriteLine("系统托盘初始化完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"初始化系统托盘时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 加载应用程序图标
    /// </summary>
    private System.Drawing.Icon LoadApplicationIcon()
    {
        try
        {
            // 方法1：尝试从应用程序资源加载图标
            var iconStream = System.Windows.Application.GetResourceStream(new Uri("pack://application:,,,/app.ico"));
            if (iconStream != null)
            {
                Console.WriteLine("成功从应用程序资源加载app.ico");
                return new System.Drawing.Icon(iconStream.Stream);
            }
            Console.WriteLine("无法从应用程序资源加载app.ico");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"从应用程序资源加载图标失败: {ex.Message}");
        }

        try
        {
            // 方法2：尝试从文件系统加载图标
            string iconPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "app.ico");
            if (System.IO.File.Exists(iconPath))
            {
                Console.WriteLine($"成功从文件系统加载app.ico: {iconPath}");
                return new System.Drawing.Icon(iconPath);
            }
            Console.WriteLine($"文件系统中找不到app.ico: {iconPath}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"从文件系统加载图标失败: {ex.Message}");
        }

        try
        {
            // 方法3：尝试从项目根目录加载图标
            string projectRoot = System.IO.Directory.GetCurrentDirectory();
            string iconPath = System.IO.Path.Combine(projectRoot, "app.ico");
            if (System.IO.File.Exists(iconPath))
            {
                Console.WriteLine($"成功从项目根目录加载app.ico: {iconPath}");
                return new System.Drawing.Icon(iconPath);
            }
            Console.WriteLine($"项目根目录中找不到app.ico: {iconPath}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"从项目根目录加载图标失败: {ex.Message}");
        }

        // 如果无法加载自定义图标，使用系统默认图标
        Console.WriteLine("使用系统默认图标");
        return System.Drawing.SystemIcons.Application;
    }

    /// <summary>
    /// 显示主窗口
    /// </summary>
    private void ShowMainWindow()
    {
        try
        {
            // 显示窗口
            Show();

            // 恢复窗口状态
            if (WindowState == WindowState.Minimized)
            {
                WindowState = WindowState.Normal;
            }

            // 激活窗口并置于前台
            Activate();
            Topmost = true;
            Topmost = false;

            Console.WriteLine("主窗口已显示");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"显示主窗口时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 退出应用程序
    /// </summary>
    public void ExitApplication()
    {
        try
        {
            _isReallyClosing = true;

            // 隐藏托盘图标
            if (_notifyIcon != null)
            {
                _notifyIcon.Visible = false;
                _notifyIcon.Dispose();
                _notifyIcon = null;
            }

            // 关闭应用程序
            System.Windows.Application.Current.Shutdown();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"退出应用程序时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 重写窗口关闭事件 - 关闭按钮(X)最小化到系统托盘
    /// </summary>
    protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
    {
        if (!_isReallyClosing)
        {
            // 取消关闭事件
            e.Cancel = true;

            // 隐藏窗口到系统托盘
            Hide();

            // 显示托盘提示
            _notifyIcon?.ShowBalloonTip(2000, "BINANCE合约排行榜", "程序已最小化到系统托盘，双击托盘图标可恢复窗口", WinForms.ToolTipIcon.Info);

            Console.WriteLine("窗口已最小化到系统托盘");
        }
        else
        {
            // 真正关闭时的清理工作
            base.OnClosing(e);
        }
    }

    /// <summary>
    /// 重写状态变化事件 - 处理最小化按钮(-)的行为
    /// </summary>
    protected override void OnStateChanged(EventArgs e)
    {
        // 如果是通过最小化按钮最小化，保持在任务栏
        if (WindowState == WindowState.Minimized)
        {
            Console.WriteLine("窗口已最小化到任务栏");
        }

        base.OnStateChanged(e);
    }

    #endregion
}