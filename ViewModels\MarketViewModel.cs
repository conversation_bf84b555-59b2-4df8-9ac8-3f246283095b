using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Threading;
using SimpleBinanceRanking.Models;
using SimpleBinanceRanking.Services;

namespace SimpleBinanceRanking.ViewModels
{
    /// <summary>
    /// 市场数据ViewModel，负责管理币安期货市场数据
    /// </summary>
    public class MarketViewModel : ViewModelBase
    {
        private readonly IBinanceService _binanceService;
        private readonly Dispatcher _dispatcher;
        private ObservableCollection<TickerData> _tickers;
        private string _connectionState;
        private DateTime _lastUpdateTime;
        private bool _isLoading;
        private bool _isInitialized;
        private string _selectedSymbol = string.Empty;
        private string _hoveredSymbol = string.Empty;
        private bool _isDisconnected = false;

        // WebSocket连接时间统计相关字段
        private DateTime _connectionStartTime;
        private TimeSpan _connectionDuration;
        private TimeSpan _remainingReconnectTime;

        // 性能优化相关字段
        private readonly int _processorCount;
        private ConcurrentDictionary<string, List<RankingData>> _symbolToRankingItemsMap; // 移除readonly，允许替换字典
        private readonly SemaphoreSlim _updateSemaphore = new SemaphoreSlim(1, 1);
        private DateTime _lastRankingUpdateTime = DateTime.MinValue;
        private DateTime _lastRestApiUpdateTime = DateTime.MinValue;
        private readonly TimeSpan _updateThrottleInterval = TimeSpan.FromMilliseconds(100); // 100ms更新一次
        private readonly TimeSpan _restApiUpdateInterval = TimeSpan.FromMinutes(10); // 10分钟更新一次REST API数据
        private bool _pendingUpdate = false;
        private System.Threading.Timer? _updateTimer;
        private System.Threading.Timer? _restApiUpdateTimer;

        /// <summary>
        /// 所有交易对的24小时行情数据
        /// </summary>
        public ObservableCollection<TickerData> Tickers
        {
            get => _tickers;
            set => SetProperty(ref _tickers, value);
        }

        /// <summary>
        /// 连接状态
        /// </summary>
        public string ConnectionState
        {
            get => _connectionState;
            set => SetProperty(ref _connectionState, value);
        }

        /// <summary>
        /// 上次更新时间
        /// </summary>
        public DateTime LastUpdateTime
        {
            get => _lastUpdateTime;
            set => SetProperty(ref _lastUpdateTime, value);
        }

        /// <summary>
        /// 是否正在加载数据
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized
        {
            get => _isInitialized;
            set => SetProperty(ref _isInitialized, value);
        }

        /// <summary>
        /// WebSocket连接开始时间
        /// </summary>
        public DateTime ConnectionStartTime
        {
            get => _connectionStartTime;
            set => SetProperty(ref _connectionStartTime, value);
        }

        /// <summary>
        /// WebSocket连接持续时间
        /// </summary>
        public TimeSpan ConnectionDuration
        {
            get => _connectionDuration;
            set => SetProperty(ref _connectionDuration, value);
        }

        /// <summary>
        /// 24小时重连剩余时间
        /// </summary>
        public TimeSpan RemainingReconnectTime
        {
            get => _remainingReconnectTime;
            set => SetProperty(ref _remainingReconnectTime, value);
        }

        /// <summary>
        /// 当前选中的交易对
        /// </summary>
        public string SelectedSymbol
        {
            get => _selectedSymbol;
            set => SetProperty(ref _selectedSymbol, value);
        }

        /// <summary>
        /// 当前鼠标悬停的交易对
        /// </summary>
        public string HoveredSymbol
        {
            get => _hoveredSymbol;
            private set => SetProperty(ref _hoveredSymbol, value);
        }

        /// <summary>
        /// 是否已断开连接
        /// </summary>
        public bool IsDisconnected
        {
            get => _isDisconnected;
            set => SetProperty(ref _isDisconnected, value);
        }

        // 涨跌比例统计相关字段
        private double _risingPercentage = 0.0;
        private double _fallingPercentage = 0.0;
        private int _totalSymbolCount = 0;

        /// <summary>
        /// 上涨比例（百分比）
        /// </summary>
        public double RisingPercentage
        {
            get => _risingPercentage;
            set => SetProperty(ref _risingPercentage, value);
        }

        /// <summary>
        /// 下跌比例（百分比）
        /// </summary>
        public double FallingPercentage
        {
            get => _fallingPercentage;
            set => SetProperty(ref _fallingPercentage, value);
        }

        /// <summary>
        /// 总交易对数量
        /// </summary>
        public int TotalSymbolCount
        {
            get => _totalSymbolCount;
            set => SetProperty(ref _totalSymbolCount, value);
        }

        private string _windowTitle = "BINANCE合约排行榜";
        /// <summary>
        /// 窗口标题（包含涨跌比例）
        /// </summary>
        public string WindowTitle
        {
            get => _windowTitle;
            set => SetProperty(ref _windowTitle, value);
        }

        /// <summary>
        /// 直接设置高亮状态（同步方法，立即执行）
        /// </summary>
        /// <param name="symbol">要高亮的交易对符号</param>
        public void SetHighlight(string symbol)
        {
            if (string.IsNullOrEmpty(symbol))
            {
                ClearHighlight();
                return;
            }

            try
            {
                // 设置当前悬停的交易对
                HoveredSymbol = symbol;

                // 使用映射字典直接找到需要高亮的项目
                if (_symbolToRankingItemsMap.TryGetValue(symbol, out var items) && items != null && items.Count > 0)
                {
                    // 先清除所有高亮
                    ClearAllHighlights();

                    // 设置匹配项的高亮状态
                    foreach (var item in items)
                    {
                        if (item != null)
                        {
                            item.IsHighlighted = true;
                        }
                    }
                }
                else
                {
                    // 如果映射中没有找到，使用传统方法
                    // 先清除所有高亮
                    ClearAllHighlights();

                    // 更新涨幅榜
                    SetHighlightInCollection(PriceChangePercentRankingList, symbol);

                    // 更新跌幅榜
                    SetHighlightInCollection(PriceChangePercentDescRankingList, symbol);

                    // 更新振幅榜
                    SetHighlightInCollection(PriceVolatilityRankingList, symbol);

                    // 更新成交额榜
                    SetHighlightInCollection(VolumeRankingList, symbol);

                    // 更新成交笔数榜
                    SetHighlightInCollection(CountRankingList, symbol);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"设置高亮状态时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除所有高亮状态（同步方法，立即执行）
        /// </summary>
        public void ClearHighlight()
        {
            try
            {
                // 清除当前悬停的交易对
                HoveredSymbol = string.Empty;

                // 清除所有高亮
                ClearAllHighlights();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"清除高亮状态时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 在集合中设置指定符号的高亮状态
        /// </summary>
        private void SetHighlightInCollection(ObservableCollection<RankingData> collection, string symbol)
        {
            foreach (var item in collection)
            {
                if (item != null)
                {
                    item.IsHighlighted = item.Symbol.Equals(symbol, StringComparison.OrdinalIgnoreCase);
                }
            }
        }

        /// <summary>
        /// 清除所有排行榜中的高亮状态
        /// </summary>
        private void ClearAllHighlights()
        {
            try
            {
                // 直接在UI线程上执行，避免任何异步延迟
                // 使用快速遍历方式，只处理已高亮的项目
                foreach (var item in PriceChangePercentRankingList)
                {
                    if (item != null && item.IsHighlighted)
                    {
                        item.IsHighlighted = false;
                    }
                }

                foreach (var item in PriceChangePercentDescRankingList)
                {
                    if (item != null && item.IsHighlighted)
                    {
                        item.IsHighlighted = false;
                    }
                }

                foreach (var item in PriceVolatilityRankingList)
                {
                    if (item != null && item.IsHighlighted)
                    {
                        item.IsHighlighted = false;
                    }
                }

                foreach (var item in VolumeRankingList)
                {
                    if (item != null && item.IsHighlighted)
                    {
                        item.IsHighlighted = false;
                    }
                }

                foreach (var item in CountRankingList)
                {
                    if (item != null && item.IsHighlighted)
                    {
                        item.IsHighlighted = false;
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"清除高亮状态时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 涨幅榜数据
        /// </summary>
        public ObservableCollection<RankingData> PriceChangePercentRankingList { get; } = new ObservableCollection<RankingData>();

        /// <summary>
        /// 跌幅榜数据
        /// </summary>
        public ObservableCollection<RankingData> PriceChangePercentDescRankingList { get; } = new ObservableCollection<RankingData>();

        /// <summary>
        /// 振幅榜数据
        /// </summary>
        public ObservableCollection<RankingData> PriceVolatilityRankingList { get; } = new ObservableCollection<RankingData>();

        /// <summary>
        /// 成交额榜数据
        /// </summary>
        public ObservableCollection<RankingData> VolumeRankingList { get; } = new ObservableCollection<RankingData>();

        /// <summary>
        /// 成交笔数榜数据
        /// </summary>
        public ObservableCollection<RankingData> CountRankingList { get; } = new ObservableCollection<RankingData>();

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="binanceService">币安服务</param>
        public MarketViewModel(IBinanceService binanceService)
        {
            _binanceService = binanceService;
            _dispatcher = Dispatcher.CurrentDispatcher;
            _tickers = new ObservableCollection<TickerData>();
            _connectionState = "未连接";
            _lastUpdateTime = DateTime.Now;
            _isLoading = false;
            _isInitialized = false;

            // 初始化连接时间统计相关字段
            _connectionStartTime = DateTime.MinValue;
            _connectionDuration = TimeSpan.Zero;
            _remainingReconnectTime = TimeSpan.Zero;

            // 初始化性能优化相关字段
            _processorCount = Environment.ProcessorCount;
            LoggingService.LogInfo($"检测到 {_processorCount} 个处理器核心，将充分利用多核性能");

            // 初始化符号映射字典
            _symbolToRankingItemsMap = new ConcurrentDictionary<string, List<RankingData>>(StringComparer.OrdinalIgnoreCase);

            // 启动更新定时器
            _updateTimer = new System.Threading.Timer(UpdateTimerCallback, null, Timeout.Infinite, Timeout.Infinite);
        }

        /// <summary>
        /// 更新定时器回调
        /// </summary>
        private void UpdateTimerCallback(object? state)
        {
            if (_pendingUpdate)
            {
                _dispatcher.InvokeAsync(() =>
                {
                    UpdateRankingListsIfNeeded();
                });
            }
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        public async Task Initialize()
        {
            // 移除条件检查，允许重新初始化
            // 如果已经初始化，则先确保资源被正确释放
            if (_isInitialized)
            {
                LoggingService.LogInfo("已初始化，重置状态后重新初始化");
                // 确保资源被正确释放，但不调用StopConnection以避免重复操作
                _updateTimer?.Change(Timeout.Infinite, Timeout.Infinite);
                _restApiUpdateTimer?.Change(Timeout.Infinite, Timeout.Infinite);
            }

            IsLoading = true;
            ConnectionState = "正在连接";

            try
            {
                // 重新创建定时器（如果已被释放）
                if (_updateTimer == null)
                {
                    _updateTimer = new System.Threading.Timer(UpdateTimerCallback, null, Timeout.Infinite, Timeout.Infinite);
                }

                // 获取初始数据
                await FetchData();
                _lastRestApiUpdateTime = DateTime.Now;

                // 初始化WebSocket连接
                await InitWebSocket();

                // 启动UI更新定时器
                _updateTimer.Change(_updateThrottleInterval, _updateThrottleInterval);

                // 重新创建并启动REST API定时更新定时器
                if (_restApiUpdateTimer == null)
                {
                    _restApiUpdateTimer = new System.Threading.Timer(RestApiUpdateCallback, null, _restApiUpdateInterval, _restApiUpdateInterval);
                    LoggingService.LogInfo($"REST API数据将每 {_restApiUpdateInterval.TotalMinutes} 分钟更新一次");
                }
                else
                {
                    _restApiUpdateTimer.Change(_restApiUpdateInterval, _restApiUpdateInterval);
                }

                IsInitialized = true;
                ConnectionState = "已连接";
                IsDisconnected = false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"初始化失败: {ex.Message}");
                ConnectionState = "连接失败";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// REST API更新定时器回调
        /// </summary>
        private async void RestApiUpdateCallback(object? state)
        {
            try
            {
                LoggingService.LogInfo("执行定时REST API数据更新...");
                await FetchData();
                _lastRestApiUpdateTime = DateTime.Now;
                LoggingService.LogInfo($"REST API数据更新完成，下次更新时间: {DateTime.Now.Add(_restApiUpdateInterval)}");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"REST API定时更新失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从REST API获取数据
        /// </summary>
        private async Task FetchData()
        {
            try
            {
                var data = await _binanceService.GetTickerDataAsync();

                _dispatcher.Invoke(() =>
                {
                    Tickers.Clear();
                    foreach (var ticker in data)
                    {
                        Tickers.Add(ticker);
                    }
                    LastUpdateTime = DateTime.Now;

                    // 保存当前高亮的交易对
                    string currentHighlightedSymbol = HoveredSymbol;

                    // 获取有效的交易对列表
                    var validTickers = Tickers.Where(IsValidTicker).ToList();

                    // 更新排行榜
                    UpdatePriceChangePercentRanking(validTickers);
                    UpdatePriceChangePercentDescRanking(validTickers);
                    UpdatePriceVolatilityRanking(validTickers);
                    UpdateVolumeRanking(validTickers);
                    UpdateCountRanking(validTickers);

                    // 更新符号映射
                    UpdateSymbolMapping();

                    // 更新涨跌比例统计
                    UpdateRiseFallStatistics(validTickers);

                    // 如果有高亮的交易对，恢复高亮状态
                    if (!string.IsNullOrEmpty(currentHighlightedSymbol))
                    {
                        SetHighlight(currentHighlightedSymbol);
                        LoggingService.LogInfo($"REST API数据更新后恢复高亮状态: {currentHighlightedSymbol}");
                    }
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"获取数据错误: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 初始化WebSocket连接
        /// </summary>
        private async Task InitWebSocket()
        {
            try
            {
                await _binanceService.ConnectToTickerStreamAsync(HandleWebSocketMessage);
                ConnectionState = _binanceService.GetConnectionStatus();

                // 添加定时检查WebSocket连接状态的任务
                _ = Task.Run(async () =>
                {
                    while (IsInitialized && !IsDisconnected)
                    {
                        try
                        {
                            // 每5秒检查一次连接状态
                            await Task.Delay(5000);

                            // 获取最新的连接状态
                            string currentStatus = _binanceService.GetConnectionStatus();

                            // 如果状态发生变化，更新UI
                            if (ConnectionState != currentStatus)
                            {
                                ConnectionState = currentStatus;
                                LoggingService.LogInfo($"WebSocket连接状态更新: {currentStatus}");
                            }

                            // 更新连接时间统计信息
                            UpdateConnectionTimeStats();
                        }
                        catch (Exception ex)
                        {
                            LoggingService.LogError($"检查WebSocket状态时出错: {ex.Message}");
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"初始化WebSocket失败: {ex.Message}");
                ConnectionState = "连接失败";
                throw;
            }
        }

        /// <summary>
        /// 处理WebSocket消息
        /// </summary>
        /// <param name="tickerDataList">行情数据列表</param>
        private void HandleWebSocketMessage(List<TickerData> tickerDataList)
        {
            if (tickerDataList == null || tickerDataList.Count == 0)
                return;

            try
            {
                // 创建临时字典以加速查找
                var tickerDict = new Dictionary<string, TickerData>();
                foreach (var ticker in tickerDataList)
                {
                    if (!string.IsNullOrEmpty(ticker.Symbol))
                    {
                        tickerDict[ticker.Symbol] = ticker;
                    }
                }

                if (tickerDict.Count == 0)
                    return;

                // 在UI线程上批量更新Tickers集合
                _dispatcher.Invoke(() =>
                {
                    try
                    {
                        // 批量更新，减少UI重绘次数
                        foreach (var newTicker in tickerDict.Values)
                        {
                            var existingTicker = Tickers.FirstOrDefault(t => t.Symbol == newTicker.Symbol);
                            if (existingTicker != null)
                            {
                                int index = Tickers.IndexOf(existingTicker);
                                Tickers[index] = newTicker;
                            }
                            else
                            {
                                Tickers.Add(newTicker);
                            }
                        }

                        // 更新时间戳
                        LastUpdateTime = DateTime.Now;
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError($"更新Tickers集合时发生错误: {ex.Message}");
                    }
                });

                // 标记需要更新排行榜
                _pendingUpdate = true;

                // 检查是否应该立即更新UI
                var now = DateTime.Now;
                if ((now - _lastRankingUpdateTime) >= _updateThrottleInterval)
                {
                    // 使用低优先级调用，避免阻塞UI线程
                    _dispatcher.InvokeAsync(() =>
                    {
                        if (_pendingUpdate)
                        {
                            try
                            {
                                UpdateRankingListsIfNeeded();
                                _lastRankingUpdateTime = now;
                            }
                            catch (Exception ex)
                            {
                                LoggingService.LogError($"调用UpdateRankingListsIfNeeded时发生错误: {ex.Message}");
                            }
                        }
                    }, DispatcherPriority.Background);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"处理WebSocket消息时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据需要更新排行榜
        /// </summary>
        private void UpdateRankingListsIfNeeded()
        {
            if (!_pendingUpdate) return;

            try
            {
                // 使用信号量确保一次只有一个更新操作，但不要阻塞UI线程
                bool semaphoreAcquired = false;
                try
                {
                    semaphoreAcquired = _updateSemaphore.Wait(0);
                    if (!semaphoreAcquired)
                    {
                        // 如果无法获取信号量，说明另一个更新操作正在进行，直接返回
                        return;
                    }

                    var sw = Stopwatch.StartNew();

                    // 在UI线程上安全地获取Tickers的副本
                    List<TickerData> tickersCopy = new List<TickerData>();
                    _dispatcher.Invoke(() =>
                    {
                        // 检查Tickers集合是否为空
                        if (Tickers.Count == 0)
                        {
                            LoggingService.LogInfo("Tickers集合为空，无法更新排行榜");
                            return;
                        }

                        // 创建Tickers的副本，避免在处理过程中被修改
                        tickersCopy = new List<TickerData>(Tickers);
                    });

                    if (tickersCopy.Count == 0)
                    {
                        return;
                    }

                    // 在后台线程上过滤有效的交易对
                    var validTickers = tickersCopy.Where(IsValidTicker).ToList();

                    if (validTickers.Count == 0)
                    {
                        LoggingService.LogInfo("没有有效的交易对，无法更新排行榜");
                        return;
                    }

                    LoggingService.LogInfo($"找到 {validTickers.Count} 个有效交易对，开始更新排行榜");

                    // 顺序执行排行榜更新，避免并行处理可能导致的问题
                    UpdatePriceChangePercentRanking(validTickers);
                    UpdatePriceChangePercentDescRanking(validTickers);
                    UpdatePriceVolatilityRanking(validTickers);
                    UpdateVolumeRanking(validTickers);
                    UpdateCountRanking(validTickers);

                    // 保存当前高亮的交易对
                    string currentHighlightedSymbol = HoveredSymbol;

                    // 更新符号映射
                    UpdateSymbolMapping();

                    // 更新涨跌比例统计
                    UpdateRiseFallStatistics(validTickers);

                    // 如果有高亮的交易对，恢复高亮状态
                    if (!string.IsNullOrEmpty(currentHighlightedSymbol))
                    {
                        SetHighlight(currentHighlightedSymbol);
                        LoggingService.LogInfo($"数据更新后恢复高亮状态: {currentHighlightedSymbol}");
                    }

                    sw.Stop();
                    LoggingService.LogInfo($"排行榜更新耗时: {sw.ElapsedMilliseconds}ms");
                    _pendingUpdate = false;
                }
                finally
                {
                    if (semaphoreAcquired)
                    {
                        _updateSemaphore.Release();
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"更新排行榜时发生错误: {ex.Message}\n{ex.StackTrace}");
                _pendingUpdate = false; // 确保错误不会导致永久阻塞更新
            }
        }

        /// <summary>
        /// 更新符号到排行榜项的映射
        /// </summary>
        private void UpdateSymbolMapping()
        {
            try
            {
                // 创建一个新的字典，避免清空现有字典可能导致的问题
                var newMap = new ConcurrentDictionary<string, List<RankingData>>(StringComparer.OrdinalIgnoreCase);

                // 收集所有排行榜项
                var allItems = new List<RankingData>();

                // 使用UI线程安全地收集项目
                _dispatcher.Invoke(() =>
                {
                    allItems.AddRange(PriceChangePercentRankingList);
                    allItems.AddRange(PriceChangePercentDescRankingList);
                    allItems.AddRange(PriceVolatilityRankingList);
                    allItems.AddRange(VolumeRankingList);
                    allItems.AddRange(CountRankingList);
                });

                // 顺序处理项目，避免并行处理可能导致的问题
                foreach (var item in allItems)
                {
                    if (item == null || string.IsNullOrEmpty(item.Symbol))
                        continue;

                    newMap.AddOrUpdate(
                        item.Symbol,
                        new List<RankingData> { item },
                        (key, existingList) =>
                        {
                            existingList.Add(item);
                            return existingList;
                        });
                }

                // 替换旧映射
                _symbolToRankingItemsMap = newMap;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"更新符号映射时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新涨幅榜
        /// </summary>
        /// <param name="validTickers">预先过滤的有效交易对列表</param>
        private void UpdatePriceChangePercentRanking(List<TickerData> validTickers)
        {
            try
            {
                // 创建本地副本以避免处理中的竞争条件
                var localTickers = new List<TickerData>(validTickers);

                // 使用普通LINQ排序，避免并行处理可能导致的问题
                var sortedTickers = localTickers
                    .OrderByDescending(ticker =>
                    {
                        double.TryParse(ticker.PriceChangePercent, out var val);
                        return val;
                    })
                    .Take(50)
                    .ToList();

                // 预先创建RankingData对象，减少UI线程负担
                var newItems = new List<RankingData>();
                int rank = 1;
                foreach (var ticker in sortedTickers)
                {
                    double value = double.TryParse(ticker.PriceChangePercent, out var val) ? val : 0;
                    double price = double.TryParse(ticker.LastPrice, out var p) ? p : 0;
                    newItems.Add(new RankingData(ticker.Symbol, value, price, rank++));
                }

                // 在UI线程上批量更新集合
                _dispatcher.Invoke(() =>
                {
                    // 使用批量更新减少UI重绘次数
                    PriceChangePercentRankingList.Clear();
                    foreach (var item in newItems)
                    {
                        PriceChangePercentRankingList.Add(item);
                    }
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"更新涨幅榜时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新跌幅榜
        /// </summary>
        /// <param name="validTickers">预先过滤的有效交易对列表</param>
        private void UpdatePriceChangePercentDescRanking(List<TickerData> validTickers)
        {
            try
            {
                // 创建本地副本以避免处理中的竞争条件
                var localTickers = new List<TickerData>(validTickers);

                // 使用普通LINQ排序，避免并行处理可能导致的问题
                var sortedTickers = localTickers
                    .OrderBy(ticker =>
                    {
                        double.TryParse(ticker.PriceChangePercent, out var val);
                        return val;
                    })
                    .Take(50)
                    .ToList();

                // 预先创建RankingData对象，减少UI线程负担
                var newItems = new List<RankingData>();
                int rank = 1;
                foreach (var ticker in sortedTickers)
                {
                    double value = double.TryParse(ticker.PriceChangePercent, out var val) ? val : 0;
                    double price = double.TryParse(ticker.LastPrice, out var p) ? p : 0;
                    newItems.Add(new RankingData(ticker.Symbol, value, price, rank++));
                }

                // 在UI线程上批量更新集合
                _dispatcher.Invoke(() =>
                {
                    // 使用批量更新减少UI重绘次数
                    PriceChangePercentDescRankingList.Clear();
                    foreach (var item in newItems)
                    {
                        PriceChangePercentDescRankingList.Add(item);
                    }
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"更新跌幅榜时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新振幅榜
        /// </summary>
        /// <param name="validTickers">预先过滤的有效交易对列表</param>
        private void UpdatePriceVolatilityRanking(List<TickerData> validTickers)
        {
            try
            {
                // 创建本地副本以避免处理中的竞争条件
                var localTickers = new List<TickerData>(validTickers);

                // 使用普通LINQ计算振幅并排序
                var tickersWithAmplitude = localTickers
                    .Select(ticker =>
                    {
                        double high = double.TryParse(ticker.HighPrice, out var h) ? h : 0;
                        double low = double.TryParse(ticker.LowPrice, out var l) ? l : 0;
                        double open = double.TryParse(ticker.OpenPrice, out var o) ? o : 0;

                        double amplitude = 0.0;
                        if (open > 0)
                        {
                            amplitude = (high - low) / open * 100.0;
                        }

                        return new { Ticker = ticker, Amplitude = amplitude };
                    })
                    .ToList();

                var sortedTickers = tickersWithAmplitude
                    .OrderByDescending(item => item.Amplitude)
                    .Take(50)
                    .ToList();

                // 预先创建RankingData对象，减少UI线程负担
                var newItems = new List<RankingData>();
                int rank = 1;
                foreach (var item in sortedTickers)
                {
                    double price = double.TryParse(item.Ticker.LastPrice, out var p) ? p : 0;
                    newItems.Add(new RankingData(item.Ticker.Symbol, item.Amplitude, price, rank++));
                }

                // 在UI线程上批量更新集合
                _dispatcher.Invoke(() =>
                {
                    // 使用批量更新减少UI重绘次数
                    PriceVolatilityRankingList.Clear();
                    foreach (var item in newItems)
                    {
                        PriceVolatilityRankingList.Add(item);
                    }
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"更新振幅榜时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新成交额榜
        /// </summary>
        /// <param name="validTickers">预先过滤的有效交易对列表</param>
        private void UpdateVolumeRanking(List<TickerData> validTickers)
        {
            try
            {
                // 创建本地副本以避免处理中的竞争条件
                var localTickers = new List<TickerData>(validTickers);

                // 使用普通LINQ排序，避免并行处理可能导致的问题
                var sortedTickers = localTickers
                    .OrderByDescending(ticker =>
                    {
                        double.TryParse(ticker.QuoteVolume, out var val);
                        return val;
                    })
                    .Take(50)
                    .ToList();

                // 预先创建RankingData对象，减少UI线程负担
                var newItems = new List<RankingData>();
                int rank = 1;
                foreach (var ticker in sortedTickers)
                {
                    double value = double.TryParse(ticker.QuoteVolume, out var val) ? val / 1000000000 : 0; // 除以十亿，转换为B单位
                    double price = double.TryParse(ticker.LastPrice, out var p) ? p : 0;
                    newItems.Add(new RankingData(ticker.Symbol, value, price, rank++));
                }

                // 在UI线程上批量更新集合
                _dispatcher.Invoke(() =>
                {
                    // 使用批量更新减少UI重绘次数
                    VolumeRankingList.Clear();
                    foreach (var item in newItems)
                    {
                        VolumeRankingList.Add(item);
                    }
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"更新成交额榜时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新成交笔数榜
        /// </summary>
        /// <param name="validTickers">预先过滤的有效交易对列表</param>
        private void UpdateCountRanking(List<TickerData> validTickers)
        {
            try
            {
                // 创建本地副本以避免处理中的竞争条件
                var localTickers = new List<TickerData>(validTickers);

                // 使用普通LINQ排序，避免并行处理可能导致的问题
                var sortedTickers = localTickers
                    .OrderByDescending(ticker => ticker.Count)
                    .Take(50)
                    .ToList();

                // 预先创建RankingData对象，减少UI线程负担
                var newItems = new List<RankingData>();
                int rank = 1;
                foreach (var ticker in sortedTickers)
                {
                    double price = double.TryParse(ticker.LastPrice, out var p) ? p : 0;
                    // 将成交笔数转换为百万(M)单位
                    double countInMillions = ticker.Count / 1_000_000.0;
                    newItems.Add(new RankingData(ticker.Symbol, countInMillions, price, rank++));
                }

                // 在UI线程上批量更新集合
                _dispatcher.Invoke(() =>
                {
                    // 使用批量更新减少UI重绘次数
                    CountRankingList.Clear();
                    foreach (var item in newItems)
                    {
                        CountRankingList.Add(item);
                    }
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"更新成交笔数榜时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 判断交易对是否有效
        /// </summary>
        /// <param name="ticker">行情数据</param>
        /// <returns>是否有效</returns>
        private bool IsValidTicker(TickerData ticker)
        {
            // 1. 交易对过滤 - 只保留USDT结尾的交易对，排除USDC结尾的交易对
            bool isValidSymbol = ticker.Symbol.EndsWith("USDT") && !ticker.Symbol.Contains("_") && !ticker.Symbol.EndsWith("USDC");

            // 2. 数值有效性过滤 - 排除价格为0或成交量为0的交易对
            double price = double.TryParse(ticker.LastPrice, out var p) ? p : 0;
            double volume = double.TryParse(ticker.Volume, out var v) ? v : 0;
            bool hasValidValues = price > 0 && volume > 0;

            // 3. 时效性过滤 - 排除数据时间戳超过1小时的交易对
            long currentTimeMs = DateTimeOffset.Now.ToUnixTimeMilliseconds();
            long oneHourMs = 60 * 60 * 1000; // 1小时的毫秒数
            bool isTimely = (currentTimeMs - ticker.CloseTime) < oneHourMs;

            return isValidSymbol && hasValidValues && isTimely;
        }

        /// <summary>
        /// 更新连接时间统计信息
        /// </summary>
        private void UpdateConnectionTimeStats()
        {
            try
            {
                if (ConnectionState.Contains("已连接"))
                {
                    // 从BinanceService获取连接开始时间
                    var serviceConnectionStartTime = _binanceService.GetConnectionStartTime();

                    // 如果BinanceService有有效的连接开始时间，使用它
                    if (serviceConnectionStartTime != DateTime.MinValue)
                    {
                        // 转换为本地时间
                        var localConnectionStartTime = serviceConnectionStartTime.ToLocalTime();

                        // 检查是否是新的连接（连接开始时间发生了变化）
                        if (ConnectionStartTime == DateTime.MinValue ||
                            Math.Abs((ConnectionStartTime - localConnectionStartTime).TotalSeconds) > 60)
                        {
                            ConnectionStartTime = localConnectionStartTime;
                            LoggingService.LogInfo($"检测到新连接，连接开始时间: {ConnectionStartTime:yyyy-MM-dd HH:mm:ss}");
                        }
                    }
                    else if (ConnectionStartTime == DateTime.MinValue)
                    {
                        // 如果连接开始时间还未设置，设置为当前时间
                        ConnectionStartTime = DateTime.Now;
                        LoggingService.LogInfo($"设置连接开始时间为当前时间: {ConnectionStartTime:yyyy-MM-dd HH:mm:ss}");
                    }

                    // 计算连接持续时间
                    ConnectionDuration = DateTime.Now - ConnectionStartTime;

                    // 计算24小时重连剩余时间
                    var totalLifetime = TimeSpan.FromHours(24);
                    var remaining = totalLifetime - ConnectionDuration;
                    RemainingReconnectTime = remaining > TimeSpan.Zero ? remaining : TimeSpan.Zero;
                }
                else
                {
                    // 连接断开或其他状态时，重置时间统计
                    if (ConnectionStartTime != DateTime.MinValue)
                    {
                        LoggingService.LogInfo("连接状态非已连接，重置连接时间统计");
                        ResetConnectionTimeStats();
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"更新连接时间统计时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 停止连接
        /// </summary>
        public void StopConnection()
        {
            try
            {
                // 停止UI更新定时器
                _updateTimer?.Change(Timeout.Infinite, Timeout.Infinite);
                _updateTimer?.Dispose();
                _updateTimer = null;

                // 停止REST API更新定时器
                _restApiUpdateTimer?.Change(Timeout.Infinite, Timeout.Infinite);
                _restApiUpdateTimer?.Dispose();
                _restApiUpdateTimer = null;

                // 释放信号量，但不要Dispose它，因为重新连接时需要使用
                // 只是释放当前持有的信号量
                if (_updateSemaphore.CurrentCount == 0)
                {
                    _updateSemaphore.Release();
                }

                // 断开WebSocket连接
                _binanceService.DisconnectWebSockets();
                ConnectionState = _binanceService.GetConnectionStatus();

                // 设置断开连接状态
                IsDisconnected = true;
                IsInitialized = false; // 重置初始化标志，确保可以重新初始化

                // 手动断开连接时重置连接时间统计
                ResetConnectionTimeStats();

                LoggingService.LogInfo("已停止所有连接和资源");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"停止连接失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新窗口标题
        /// </summary>
        private void UpdateWindowTitle()
        {
            try
            {
                if (TotalSymbolCount > 0)
                {
                    // 格式：81:19 BINANCE合约排行榜（四舍五入，不显示小数点）
                    WindowTitle = $"比例 {RisingPercentage:F0} : {FallingPercentage:F0} BINANCE合约排行榜";
                }
                else
                {
                    // 没有数据时显示默认标题
                    WindowTitle = "BINANCE合约排行榜";
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"更新窗口标题时发生错误: {ex.Message}");
                WindowTitle = "BINANCE合约排行榜";
            }
        }

        /// <summary>
        /// 更新涨跌比例统计
        /// </summary>
        /// <param name="validTickers">有效的交易对列表</param>
        private void UpdateRiseFallStatistics(List<TickerData> validTickers)
        {
            try
            {
                if (validTickers == null || validTickers.Count == 0)
                {
                    // 如果没有有效数据，重置统计
                    _dispatcher.Invoke(() =>
                    {
                        TotalSymbolCount = 0;
                        RisingPercentage = 0.0;
                        FallingPercentage = 0.0;
                        UpdateWindowTitle();
                    });
                    return;
                }

                int totalCount = validTickers.Count;
                int risingCount = 0;
                int fallingCount = 0;

                // 统计涨跌情况（简化：只有涨或跌两种情况，等于0%归为下跌）
                foreach (var ticker in validTickers)
                {
                    if (double.TryParse(ticker.PriceChangePercent, out var changePercent))
                    {
                        if (changePercent > 0)
                        {
                            risingCount++;
                        }
                        else
                        {
                            // 等于0%或小于0%都归为下跌
                            fallingCount++;
                        }
                    }
                }

                // 计算百分比（四舍五入到整数）
                double risingPercentage = totalCount > 0 ? Math.Round((double)risingCount / totalCount * 100.0) : 0.0;
                double fallingPercentage = totalCount > 0 ? Math.Round((double)fallingCount / totalCount * 100.0) : 0.0;

                // 在UI线程上更新属性
                _dispatcher.Invoke(() =>
                {
                    TotalSymbolCount = totalCount;
                    RisingPercentage = risingPercentage;
                    FallingPercentage = fallingPercentage;

                    // 更新窗口标题
                    UpdateWindowTitle();
                });

                LoggingService.LogInfo($"涨跌统计更新: 总数={totalCount}, 上涨={risingCount}({risingPercentage:F0}%), 下跌={fallingCount}({fallingPercentage:F0}%)");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"更新涨跌比例统计时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置连接时间统计
        /// </summary>
        private void ResetConnectionTimeStats()
        {
            ConnectionStartTime = DateTime.MinValue;
            ConnectionDuration = TimeSpan.Zero;
            RemainingReconnectTime = TimeSpan.Zero;
            LoggingService.LogInfo("连接时间统计已重置");
        }
    }
}


