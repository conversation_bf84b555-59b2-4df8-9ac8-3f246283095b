<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>true</UseWindowsForms>
    <!-- 高DPI设置 -->
    <ApplicationHighDpiMode>PerMonitorV2</ApplicationHighDpiMode>
    <!-- 应用程序图标 -->
    <ApplicationIcon>app.ico</ApplicationIcon>
    <!-- 禁用默认编译项，以便我们可以显式指定要编译的文件 -->
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <!-- 引用应用程序清单文件，启用DPI感知 -->
    <ApplicationManifest>app.manifest</ApplicationManifest>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MaterialDesignColors" Version="5.2.1" />
    <PackageReference Include="MaterialDesignThemes" Version="5.2.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.5" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Management" Version="9.0.5" />
    <PackageReference Include="Websocket.Client" Version="5.1.2" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.5" />

  </ItemGroup>

  <!-- 显式指定要编译的文件 -->
  <ItemGroup>
    <!-- 主项目文件 -->
    <Compile Include="**\*.cs" Exclude="obj\**\*.cs" />

    <!-- 配置文件 -->
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.test.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>

    <!-- 应用程序图标文件 -->
    <Resource Include="app.ico" />
  </ItemGroup>

</Project>



