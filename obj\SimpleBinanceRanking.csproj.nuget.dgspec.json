{"format": 1, "restore": {"D:\\DeepResearch\\250522_C#WPF_实现BNU排行榜@WIN机使用\\SimpleBinanceRanking\\SimpleBinanceRanking.csproj": {}}, "projects": {"D:\\DeepResearch\\250522_C#WPF_实现BNU排行榜@WIN机使用\\SimpleBinanceRanking\\SimpleBinanceRanking.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\DeepResearch\\250522_C#WPF_实现BNU排行榜@WIN机使用\\SimpleBinanceRanking\\SimpleBinanceRanking.csproj", "projectName": "SimpleBinanceRanking", "projectPath": "D:\\DeepResearch\\250522_C#WPF_实现BNU排行榜@WIN机使用\\SimpleBinanceRanking\\SimpleBinanceRanking.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\DeepResearch\\250522_C#WPF_实现BNU排行榜@WIN机使用\\SimpleBinanceRanking\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"MaterialDesignColors": {"target": "Package", "version": "[5.2.1, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[5.2.1, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.5, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Drawing.Common": {"target": "Package", "version": "[9.0.5, )"}, "System.Management": {"target": "Package", "version": "[9.0.5, )"}, "Websocket.Client": {"target": "Package", "version": "[5.1.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}