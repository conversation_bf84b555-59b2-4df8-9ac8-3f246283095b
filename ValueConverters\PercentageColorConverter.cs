using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace SimpleBinanceRanking.ValueConverters
{
    /// <summary>
    /// 根据百分比值返回相应颜色的转换器
    /// 正值返回绿色，负值返回红色
    /// </summary>
    public class PercentageColorConverter : IValueConverter
    {
        // 绿色画刷 (涨)
        private static readonly SolidColorBrush GreenBrush = new SolidColorBrush(System.Windows.Media.Color.FromRgb(14, 203, 129)); // #0ECB81

        // 红色画刷 (跌)
        private static readonly SolidColorBrush RedBrush = new SolidColorBrush(System.Windows.Media.Color.FromRgb(246, 70, 93)); // #F6465D

        static PercentageColorConverter()
        {
            // 冻结画刷以提高性能
            GreenBrush.Freeze();
            RedBrush.Freeze();
        }

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double percentage)
            {
                return percentage >= 0 ? GreenBrush : RedBrush;
            }

            // 默认返回红色
            return RedBrush;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 