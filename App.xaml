<Application x:Class="SimpleBinanceRanking.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:SimpleBinanceRanking"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- MaterialDesign主题 -->
                <materialDesign:BundledTheme BaseTheme="Dark" PrimaryColor="Amber" SecondaryColor="Amber" />

                <!-- MaterialDesign主题资源 -->
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/Generic.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- 深色主题资源字典 -->
            <ResourceDictionary x:Key="DarkThemeDict">
                <SolidColorBrush x:Key="WindowBackgroundBrush" Color="#0B0E11"/>
                <SolidColorBrush x:Key="WindowForegroundBrush" Color="#EAECEF"/>
                <SolidColorBrush x:Key="HeaderBackgroundColor" Color="#161A1E"/>
                <SolidColorBrush x:Key="CardBackgroundColor" Color="#161A1E"/>
                <SolidColorBrush x:Key="FooterBackgroundColor" Color="#161A1E"/>
                <SolidColorBrush x:Key="PrimaryTextColor" Color="#EAECEF"/>
                <SolidColorBrush x:Key="SecondaryTextColor" Color="#848E9C"/>
                <SolidColorBrush x:Key="BorderBrush" Color="#222222"/>

                <!-- 排行榜颜色 -->
                <SolidColorBrush x:Key="GreenColor" Color="#0ECB81"/>
                <SolidColorBrush x:Key="RedColor" Color="#F6465D"/>
                <SolidColorBrush x:Key="YellowColor" Color="#F0B90B"/>
                <SolidColorBrush x:Key="CmcBlueColor" Color="#3861FB"/>
                <SolidColorBrush x:Key="CountColor" Color="#9B59B6"/>

                <!-- 悬停和高亮时的透明度版本 -->
                <SolidColorBrush x:Key="GreenColorHover" Color="#0ECB81" Opacity="0.15"/>
                <SolidColorBrush x:Key="RedColorHover" Color="#F6465D" Opacity="0.15"/>
                <SolidColorBrush x:Key="YellowColorHover" Color="#F0B90B" Opacity="0.15"/>
                <SolidColorBrush x:Key="CmcBlueColorHover" Color="#3861FB" Opacity="0.15"/>
                <SolidColorBrush x:Key="CountColorHover" Color="#9B59B6" Opacity="0.15"/>
            </ResourceDictionary>

            <!-- 浅色主题资源字典 -->
            <ResourceDictionary x:Key="LightThemeDict">
                <SolidColorBrush x:Key="WindowBackgroundBrush" Color="#F5F5F5"/>
                <SolidColorBrush x:Key="WindowForegroundBrush" Color="#121212"/>
                <SolidColorBrush x:Key="HeaderBackgroundColor" Color="#FFFFFF"/>
                <SolidColorBrush x:Key="CardBackgroundColor" Color="#FFFFFF"/>
                <SolidColorBrush x:Key="FooterBackgroundColor" Color="#FFFFFF"/>
                <SolidColorBrush x:Key="PrimaryTextColor" Color="#121212"/>
                <SolidColorBrush x:Key="SecondaryTextColor" Color="#757575"/>
                <SolidColorBrush x:Key="BorderBrush" Color="#DDDDDD"/>

                <!-- 排行榜颜色（浅色主题下稍微调整透明度） -->
                <SolidColorBrush x:Key="GreenColor" Color="#0ECB81"/>
                <SolidColorBrush x:Key="RedColor" Color="#F6465D"/>
                <SolidColorBrush x:Key="YellowColor" Color="#F0B90B"/>
                <SolidColorBrush x:Key="CmcBlueColor" Color="#3861FB"/>
                <SolidColorBrush x:Key="CountColor" Color="#9B59B6"/>

                <!-- 悬停和高亮时的透明度版本（浅色主题下透明度稍高） -->
                <SolidColorBrush x:Key="GreenColorHover" Color="#0ECB81" Opacity="0.2"/>
                <SolidColorBrush x:Key="RedColorHover" Color="#F6465D" Opacity="0.2"/>
                <SolidColorBrush x:Key="YellowColorHover" Color="#F0B90B" Opacity="0.2"/>
                <SolidColorBrush x:Key="CmcBlueColorHover" Color="#3861FB" Opacity="0.2"/>
                <SolidColorBrush x:Key="CountColorHover" Color="#9B59B6" Opacity="0.2"/>
            </ResourceDictionary>

            <!-- 默认使用深色主题资源 -->
            <SolidColorBrush x:Key="WindowBackgroundBrush" Color="#0B0E11"/>
            <SolidColorBrush x:Key="WindowForegroundBrush" Color="#EAECEF"/>
            <SolidColorBrush x:Key="HeaderBackgroundColor" Color="#161A1E"/>
            <SolidColorBrush x:Key="CardBackgroundColor" Color="#161A1E"/>
            <SolidColorBrush x:Key="FooterBackgroundColor" Color="#161A1E"/>
            <SolidColorBrush x:Key="PrimaryTextColor" Color="#EAECEF"/>
            <SolidColorBrush x:Key="SecondaryTextColor" Color="#848E9C"/>
            <SolidColorBrush x:Key="BorderBrush" Color="#222222"/>

            <!-- 默认排行榜颜色 -->
            <SolidColorBrush x:Key="GreenColor" Color="#0ECB81"/>
            <SolidColorBrush x:Key="RedColor" Color="#F6465D"/>
            <SolidColorBrush x:Key="YellowColor" Color="#F0B90B"/>
            <SolidColorBrush x:Key="CmcBlueColor" Color="#3861FB"/>
            <SolidColorBrush x:Key="CountColor" Color="#9B59B6"/>

            <!-- 默认悬停和高亮时的透明度版本 -->
            <SolidColorBrush x:Key="GreenColorHover" Color="#0ECB81" Opacity="0.15"/>
            <SolidColorBrush x:Key="RedColorHover" Color="#F6465D" Opacity="0.15"/>
            <SolidColorBrush x:Key="YellowColorHover" Color="#F0B90B" Opacity="0.15"/>
            <SolidColorBrush x:Key="CmcBlueColorHover" Color="#3861FB" Opacity="0.15"/>
            <SolidColorBrush x:Key="CountColorHover" Color="#9B59B6" Opacity="0.15"/>

            <!-- 全局GPU加速设置 -->
            <RenderOptions x:Key="GlobalRenderOptions"
                          BitmapScalingMode="HighQuality"
                          EdgeMode="Aliased"
                          ClearTypeHint="Enabled"
                          CachingHint="Cache" />

            <!-- 启用硬件加速和高质量渲染 - 优化字体清晰度 -->
            <Style TargetType="{x:Type Window}" BasedOn="{StaticResource {x:Type Window}}">
                <Setter Property="Background" Value="#FF303030" />
                <!-- 高质量位图缩放，确保图像和字体清晰 -->
                <Setter Property="RenderOptions.BitmapScalingMode" Value="HighQuality" />
                <!-- 使用Unspecified边缘模式以获得最佳字体渲染 -->
                <Setter Property="RenderOptions.EdgeMode" Value="Unspecified" />
                <!-- 启用渲染缓存以提高性能 -->
                <Setter Property="RenderOptions.CachingHint" Value="Cache" />
                <!-- 启用ClearType字体平滑技术 -->
                <Setter Property="RenderOptions.ClearTypeHint" Value="Enabled" />
                <!-- 使用ClearType文本渲染模式，在LCD显示器上提供最佳清晰度 -->
                <Setter Property="TextOptions.TextRenderingMode" Value="ClearType" />
                <!-- 使用Display文本格式化模式，优化屏幕显示 -->
                <Setter Property="TextOptions.TextFormattingMode" Value="Display" />
                <!-- 启用文本提示以改善小字体的清晰度 -->
                <Setter Property="TextOptions.TextHintingMode" Value="Auto" />
                <!-- 启用布局舍入，确保像素对齐 -->
                <Setter Property="UseLayoutRounding" Value="True" />
                <!-- 启用设备像素对齐，防止模糊 -->
                <Setter Property="SnapsToDevicePixels" Value="True" />
                <!-- GPU加速渲染在代码中设置，不能在XAML样式中设置 -->
            </Style>

            <!-- 全局启用硬件加速 - ListView优化字体清晰度 -->
            <Style TargetType="{x:Type ListView}">
                <!-- 高质量位图缩放 -->
                <Setter Property="RenderOptions.BitmapScalingMode" Value="HighQuality" />
                <!-- 启用渲染缓存 -->
                <Setter Property="RenderOptions.CachingHint" Value="Cache" />
                <!-- 启用ClearType字体平滑 -->
                <Setter Property="RenderOptions.ClearTypeHint" Value="Enabled" />
                <!-- 使用Unspecified边缘模式以获得最佳字体渲染 -->
                <Setter Property="RenderOptions.EdgeMode" Value="Unspecified" />
                <!-- 优化文本渲染 -->
                <Setter Property="TextOptions.TextRenderingMode" Value="ClearType" />
                <Setter Property="TextOptions.TextFormattingMode" Value="Display" />
                <Setter Property="TextOptions.TextHintingMode" Value="Auto" />
                <!-- 启用像素对齐 -->
                <Setter Property="UseLayoutRounding" Value="True" />
                <Setter Property="SnapsToDevicePixels" Value="True" />
                <!-- 虚拟化设置 -->
                <Setter Property="ScrollViewer.CanContentScroll" Value="True" />
                <Setter Property="VirtualizingPanel.IsVirtualizing" Value="True" />
                <Setter Property="VirtualizingPanel.VirtualizationMode" Value="Recycling" />
                <Setter Property="VirtualizingPanel.CacheLengthUnit" Value="Page" />
                <Setter Property="VirtualizingPanel.CacheLength" Value="2,2" /> <!-- 增加缓存大小 -->
                <Setter Property="VirtualizingPanel.ScrollUnit" Value="Pixel" />
                <Setter Property="ScrollViewer.IsDeferredScrollingEnabled" Value="True" />
                <Setter Property="SnapsToDevicePixels" Value="True" />
            </Style>

            <!-- 全局TextBlock字体优化 -->
            <Style TargetType="{x:Type TextBlock}">
                <Setter Property="TextOptions.TextRenderingMode" Value="ClearType" />
                <Setter Property="TextOptions.TextFormattingMode" Value="Display" />
                <Setter Property="TextOptions.TextHintingMode" Value="Auto" />
                <Setter Property="UseLayoutRounding" Value="True" />
                <Setter Property="SnapsToDevicePixels" Value="True" />
                <Setter Property="RenderOptions.ClearTypeHint" Value="Enabled" />
                <Setter Property="RenderOptions.EdgeMode" Value="Unspecified" />
            </Style>

            <!-- 全局Label字体优化 -->
            <Style TargetType="{x:Type Label}">
                <Setter Property="TextOptions.TextRenderingMode" Value="ClearType" />
                <Setter Property="TextOptions.TextFormattingMode" Value="Display" />
                <Setter Property="TextOptions.TextHintingMode" Value="Auto" />
                <Setter Property="UseLayoutRounding" Value="True" />
                <Setter Property="SnapsToDevicePixels" Value="True" />
                <Setter Property="RenderOptions.ClearTypeHint" Value="Enabled" />
                <Setter Property="RenderOptions.EdgeMode" Value="Unspecified" />
            </Style>

            <!-- 全局Button字体优化 -->
            <Style TargetType="{x:Type Button}">
                <Setter Property="TextOptions.TextRenderingMode" Value="ClearType" />
                <Setter Property="TextOptions.TextFormattingMode" Value="Display" />
                <Setter Property="TextOptions.TextHintingMode" Value="Auto" />
                <Setter Property="UseLayoutRounding" Value="True" />
                <Setter Property="SnapsToDevicePixels" Value="True" />
                <Setter Property="RenderOptions.ClearTypeHint" Value="Enabled" />
                <Setter Property="RenderOptions.EdgeMode" Value="Unspecified" />
            </Style>

            <!-- 全局TextBox字体优化 -->
            <Style TargetType="{x:Type TextBox}">
                <Setter Property="TextOptions.TextRenderingMode" Value="ClearType" />
                <Setter Property="TextOptions.TextFormattingMode" Value="Display" />
                <Setter Property="TextOptions.TextHintingMode" Value="Auto" />
                <Setter Property="UseLayoutRounding" Value="True" />
                <Setter Property="SnapsToDevicePixels" Value="True" />
                <Setter Property="RenderOptions.ClearTypeHint" Value="Enabled" />
                <Setter Property="RenderOptions.EdgeMode" Value="Unspecified" />
            </Style>



            <!-- 优化Border渲染 -->
            <Style TargetType="{x:Type Border}">
                <Setter Property="UseLayoutRounding" Value="True" />
                <Setter Property="SnapsToDevicePixels" Value="True" />
                <Setter Property="RenderOptions.CachingHint" Value="Cache" />
                <Setter Property="RenderOptions.EdgeMode" Value="Aliased" />
            </Style>

            <!-- 优化Grid渲染 -->
            <Style TargetType="{x:Type Grid}">
                <Setter Property="UseLayoutRounding" Value="True" />
                <Setter Property="SnapsToDevicePixels" Value="True" />
                <Setter Property="RenderOptions.CachingHint" Value="Cache" />
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
