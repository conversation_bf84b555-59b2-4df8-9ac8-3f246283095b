using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Interop;
using System.Windows.Media;
using System.Windows.Media.Animation;
using Microsoft.Win32;
using Microsoft.Extensions.DependencyInjection;
using SimpleBinanceRanking.Services;
using SimpleBinanceRanking.ViewModels;

using MaterialDesignThemes.Wpf;

namespace SimpleBinanceRanking;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : System.Windows.Application
{
    private readonly ServiceProvider _serviceProvider;

    // 系统主题模式（深色/浅色）
    public static bool IsDarkMode { get; private set; } = true;

    // 系统主题变化事件
    public static event EventHandler<bool>? SystemThemeChanged;

    // P/Invoke 声明 - 高级DPI感知API
    [DllImport("user32.dll")]
    private static extern bool SetProcessDPIAware();

    [DllImport("user32.dll")]
    private static extern bool SetProcessDpiAwarenessContext(IntPtr value);

    [DllImport("shcore.dll")]
    private static extern int SetProcessDpiAwareness(int value);

    // DPI感知常量
    private static readonly IntPtr DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE_V2 = new IntPtr(-4);
    private const int PROCESS_PER_MONITOR_DPI_AWARE = 2;

    // Windows 10 主题设置API
    [DllImport("UXTheme.dll", SetLastError = true, EntryPoint = "#138")]
    private static extern bool ShouldSystemUseDarkMode();

    [DllImport("user32.dll")]
    private static extern int SystemParametersInfo(int uAction, int uParam, ref int lpvParam, int fuWinIni);

    // SystemParametersInfo 常量
    private const int SPI_SETANIMATION = 0x0048;
    private const int SPI_SETCOMBOBOXANIMATION = 0x1004;
    private const int SPI_SETLISTBOXSMOOTHSCROLLING = 0x1006;
    private const int SPI_SETGRADIENTCAPTIONS = 0x1008;
    private const int SPI_SETMENUANIMATION = 0x1002;
    private const int SPI_SETSELECTIONFADE = 0x1014;
    private const int SPI_SETTOOLTIPANIMATION = 0x1016;
    private const int SPI_SETCLIENTAREAANIMATION = 0x1042;

    // ANIMATIONINFO 结构体
    [StructLayout(LayoutKind.Sequential)]
    private struct ANIMATIONINFO
    {
        public int cbSize;
        public int iMinAnimate;

        // 初始化结构体
        public static ANIMATIONINFO Create()
        {
            return new ANIMATIONINFO
            {
                cbSize = Marshal.SizeOf(typeof(ANIMATIONINFO)),
                iMinAnimate = 0 // 禁用动画
            };
        }
    }

    public App()
    {
        var services = new ServiceCollection();
        ConfigureServices(services);
        _serviceProvider = services.BuildServiceProvider();
    }

    private void ConfigureServices(IServiceCollection services)
    {
        // 注册HttpClient
        services.AddHttpClient();

        // 注册配置服务
        services.AddSingleton<IConfigurationService, ConfigurationService>();

        // 注册服务
        services.AddSingleton<IBinanceService, BinanceService>();

        // 注册FontService（静态类，不需要DI注册）

        // 注册ViewModel
        services.AddSingleton<MarketViewModel>();

        // 注册主窗口
        services.AddSingleton<MainWindow>();
    }

    protected override void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);

        // 配置日志级别（生产环境可以设置为false）
        LoggingService.IsDebugEnabled = false;  // 关闭调试日志
        LoggingService.IsInfoEnabled = true;    // 保留信息日志
        LoggingService.IsErrorEnabled = true;   // 保留错误日志

        // 优化DPI缩放
        OptimizeDpiScaling();

        // 禁用不必要的Windows视觉效果
        DisableUnnecessaryWindowsEffects();

        // 设置进程优先级
        OptimizeProcessPriority();

        // 设置系统默认字体
        SetSystemDefaultFont();

        // 配置GPU加速
        ConfigureGpuAcceleration();

        // 配置动画性能
        ConfigureAnimationPerformance();

        // 检测并应用当前系统主题
        CheckAndApplySystemTheme();

        // 注册系统主题变化的事件监听
        SystemEvents.UserPreferenceChanged += SystemEvents_UserPreferenceChanged;

        // 显示主窗口
        var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();
        mainWindow.Show();

        // 输出渲染能力信息
        LogRenderCapabilities();
    }

    /// <summary>
    /// 优化DPI缩放 - 使用最新的Per-Monitor V2 DPI感知
    /// </summary>
    private void OptimizeDpiScaling()
    {
        try
        {
            // 尝试使用最新的Per-Monitor V2 DPI感知 (Windows 10 1703+)
            bool success = false;

            try
            {
                success = SetProcessDpiAwarenessContext(DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE_V2);
                if (success)
                {
                    Console.WriteLine("已启用Per-Monitor V2 DPI感知，提供最佳字体清晰度");
                    return;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Per-Monitor V2 DPI感知设置失败: {ex.Message}");
            }

            // 回退到Per-Monitor DPI感知 (Windows 8.1+)
            try
            {
                int result = SetProcessDpiAwareness(PROCESS_PER_MONITOR_DPI_AWARE);
                if (result == 0) // S_OK
                {
                    Console.WriteLine("已启用Per-Monitor DPI感知");
                    return;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Per-Monitor DPI感知设置失败: {ex.Message}");
            }

            // 最后回退到基本DPI感知
            SetProcessDPIAware();
            Console.WriteLine("已启用基本DPI感知");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"设置DPI感知时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 禁用不必要的Windows视觉效果，提高性能
    /// </summary>
    private void DisableUnnecessaryWindowsEffects()
    {
        try
        {
            // 禁用各种动画效果
            DisableWindowsAnimation(SPI_SETCOMBOBOXANIMATION);
            DisableWindowsAnimation(SPI_SETLISTBOXSMOOTHSCROLLING);
            DisableWindowsAnimation(SPI_SETMENUANIMATION);
            DisableWindowsAnimation(SPI_SETSELECTIONFADE);
            DisableWindowsAnimation(SPI_SETTOOLTIPANIMATION);
            DisableWindowsAnimation(SPI_SETCLIENTAREAANIMATION);

            Console.WriteLine("已禁用不必要的Windows视觉效果，提高性能");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"禁用Windows视觉效果时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 禁用指定的Windows动画效果
    /// </summary>
    private void DisableWindowsAnimation(int animationType)
    {
        try
        {
            if (animationType == SPI_SETANIMATION)
            {
                // 对于SPI_SETANIMATION，需要使用ANIMATIONINFO结构
                var info = ANIMATIONINFO.Create();
                int param = info.iMinAnimate;
                SystemParametersInfo(animationType, info.cbSize, ref param, 0);
            }
            else
            {
                // 对于其他动画类型，直接设置为0
                int param = 0;
                SystemParametersInfo(animationType, 0, ref param, 0);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"禁用Windows动画效果时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 优化进程优先级
    /// </summary>
    private void OptimizeProcessPriority()
    {
        try
        {
            // 设置进程优先级为高
            Process.GetCurrentProcess().PriorityClass = ProcessPriorityClass.High;

            Console.WriteLine("已将进程优先级设置为高");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"设置进程优先级时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 设置系统默认字体（微软雅黑）
    /// </summary>
    private void SetSystemDefaultFont()
    {
        try
        {
            Console.WriteLine("设置系统默认字体（微软雅黑）...");

            // 创建微软雅黑字体族
            var microsoftYaHeiFont = new System.Windows.Media.FontFamily("Microsoft YaHei");

            // 在应用程序资源中设置默认字体
            System.Windows.Application.Current.Resources["DefaultFontFamily"] = microsoftYaHeiFont;

            Console.WriteLine("已设置应用程序默认字体为微软雅黑");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"设置系统默认字体时发生错误: {ex.Message}");

            // 如果微软雅黑不可用，使用系统默认字体
            try
            {
                var systemFont = System.Windows.SystemFonts.MessageFontFamily;
                System.Windows.Application.Current.Resources["DefaultFontFamily"] = systemFont;
                Console.WriteLine($"已回退到系统默认字体: {systemFont.Source}");
            }
            catch (Exception fallbackEx)
            {
                Console.WriteLine($"设置回退字体时发生错误: {fallbackEx.Message}");
            }
        }
    }

    /// <summary>
    /// 配置GPU加速
    /// </summary>
    private void ConfigureGpuAcceleration()
    {
        try
        {
            // 检测系统渲染能力
            var renderTier = (RenderCapability.Tier >> 16);

            // 启用硬件加速
            RenderOptions.ProcessRenderMode = RenderMode.Default;
            Console.WriteLine($"启用硬件加速渲染，渲染层级: Tier {renderTier}");

            // 根据WPF渲染限制，设置帧率为144（匹配显示器刷新率）
            // WPF内部验证会拒绝0值，所以使用有效的高帧率值
            try
            {
                Timeline.DesiredFrameRateProperty.OverrideMetadata(
                    typeof(Timeline),
                    new FrameworkPropertyMetadata { DefaultValue = 144 }); // 设置为144FPS，匹配显示器

                Console.WriteLine("成功设置最大帧率为144FPS");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置帧率时发生错误: {ex.Message}");
            }

            // 使用最高性能渲染模式
            // 注意：HwndSource没有DefaultRenderMode静态属性，修正此处代码
            RenderOptions.ProcessRenderMode = RenderMode.Default;

            // 启用最高质量的渲染
            var renderingTier = RenderCapability.Tier;
            Console.WriteLine($"当前渲染层级: {renderingTier}");

            // 配置最大GPU利用率
            // 注意：某些系统可能不支持完全解除帧率限制，但这会最大程度地减少限制
            try {
                // 尝试设置GPU线程优先级
                Process.GetCurrentProcess().PriorityClass = ProcessPriorityClass.High;

                // 禁用垂直同步 (VSync)
                // 这一步在WPF中没有直接API，但可以通过设置高帧率间接实现

                Console.WriteLine("已最大化GPU利用率，帧率限制已解除");
            }
            catch (Exception ex) {
                Console.WriteLine($"设置GPU优先级时发生错误: {ex.Message}");
            }

            Console.WriteLine($"GPU加速配置完成，渲染层级: Tier {renderTier}，已解除帧率限制");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"配置GPU加速时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 配置动画性能
    /// </summary>
    private void ConfigureAnimationPerformance()
    {
        try
        {
            // 安全设置动画帧率，避免重复注册
            try
            {
                // 使用反射判断是否已经设置过该属性
                bool isAlreadySet = false;
                try
                {
                    // 获取Timeline类型的已注册元数据
                    var metadata = Timeline.DesiredFrameRateProperty.GetMetadata(typeof(Timeline));
                    if (metadata != null && metadata is FrameworkPropertyMetadata)
                    {
                        isAlreadySet = true;
                        Console.WriteLine("检测到Timeline.DesiredFrameRateProperty元数据已注册，跳过重复设置");
                    }
                }
                catch
                {
                    // 如果无法获取元数据，则认为还未设置
                    isAlreadySet = false;
                }

                if (!isAlreadySet)
                {
                    Timeline.DesiredFrameRateProperty.OverrideMetadata(
                        typeof(Timeline),
                        new FrameworkPropertyMetadata { DefaultValue = 120 });

                    Console.WriteLine("成功设置动画帧率为120FPS");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置动画帧率时发生错误: {ex.Message}");
                // 继续执行其他设置
            }

            // 使用全局方法来优化渲染，而不再尝试修改元数据
            try
            {
                Console.WriteLine("使用全局渲染优化方法");

                // 使用全局方法为所有UI元素设置默认的渲染选项
                RenderOptions.ProcessRenderMode = RenderMode.Default;

                // 创建一个全局应用程序加载器
                EventManager.RegisterClassHandler(
                    typeof(FrameworkElement),
                    FrameworkElement.LoadedEvent,
                    new RoutedEventHandler((sender, e) =>
                    {
                        if (sender is UIElement element)
                        {
                            // 对加载的元素进行渲染优化
                            RenderOptions.SetCachingHint(element, CachingHint.Cache);
                            RenderOptions.SetBitmapScalingMode(element, BitmapScalingMode.HighQuality);

                            // 使用事件处理器监听这个元素的属性变化
                            if (element is FrameworkElement fElement)
                            {
                                var renderTransform = fElement.RenderTransform;
                                fElement.LayoutUpdated += (s, args) =>
                                {
                                    // 当布局更新时，保证渲染选项被应用
                                    if (renderTransform != fElement.RenderTransform)
                                    {
                                        RenderOptions.SetCachingHint(fElement, CachingHint.Cache);
                                    }
                                };
                            }
                        }
                    }));

                // 添加CompositionTarget.Rendering事件来优化全局渲染
                System.Windows.Media.CompositionTarget.Rendering += (sender, e) =>
                {
                    // 仅定期输出信息，避免频繁日志
                    // 这里留空处理器，只是为了绑定事件使其正常工作
                };

                Console.WriteLine("成功应用全局渲染优化方法");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置渲染优化时发生错误: {ex.Message}");
            }

            // 优化复杂UI元素的渲染性能
            // 我们不能直接设置全局渲染属性，因为这些方法需要DependencyObject实例
            // 而在应用程序级别我们无法直接访问到窗口对象

            // 我们将依赖XAML中定义的全局渲染选项
            // 这些设置已经在App.xaml中配置
            // 可以在窗口创建后在具体实例上设置这些属性

            // 组件级别的渲染设置会在XAML中定义

            Console.WriteLine("已优化动画性能设置，动画帧率限制已完全解除");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"配置动画性能时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 输出渲染能力信息
    /// </summary>
    /// <summary>
    /// 检测并应用系统主题
    /// </summary>
    public static void CheckAndApplySystemTheme()
    {
        try
        {
            // 获取系统主题模式（Windows 10/11）
            bool isDarkMode = IsSystemDarkModeEnabled();
            IsDarkMode = isDarkMode;

            // 应用主题
            ApplyTheme(isDarkMode);

            Console.WriteLine($"检测到系统主题: {(isDarkMode ? "深色模式" : "浅色模式")}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"检测系统主题时发生错误: {ex.Message}");
            // 默认使用深色主题
            IsDarkMode = true;
            ApplyTheme(true);
        }
    }

    /// <summary>
    /// 检测系统是否启用了深色模式
    /// </summary>
    /// <returns>是否为深色模式</returns>
    private static bool IsSystemDarkModeEnabled()
    {
        try
        {
            // Windows 10/11 主题检测
            // 首选方法：使用注册表检测，这是最可靠的方法
            using var personalizationKey = Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize");
            if (personalizationKey != null)
            {
                // AppsUseLightTheme: 0=深色模式, 1=浅色模式
                var appsUseLightTheme = personalizationKey.GetValue("AppsUseLightTheme");
                if (appsUseLightTheme != null)
                {
                    Console.WriteLine($"从注册表检测到系统主题: {(((int)appsUseLightTheme == 0) ? "深色模式" : "浅色模式")}");
                    return ((int)appsUseLightTheme == 0);
                }
            }

            // 备用方法：尝试使用UXTheme.dll方法
            try
            {
                bool isDarkMode = ShouldSystemUseDarkMode();
                Console.WriteLine($"从 UXTheme.dll 检测到系统主题: {(isDarkMode ? "深色模式" : "浅色模式")}");
                return isDarkMode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"UXTheme.dll 方法调用失败: {ex.Message}");
            }

            // 默认使用深色模式
            Console.WriteLine("无法检测系统主题，默认使用深色模式");
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"检测系统深色模式时发生错误: {ex.Message}");
            return true; // 默认返回深色模式
        }
    }

    /// <summary>
    /// 应用主题
    /// </summary>
    /// <param name="isDarkMode">是否为深色模式</param>
    private static void ApplyTheme(bool isDarkMode)
    {
        var app = Current as App;
        if (app == null) return;

        // 获取资源字典
        var mergedDicts = app.Resources.MergedDictionaries;
        if (mergedDicts.Count == 0) return;

        // 应用主题资源
        ResourceDictionary? themeDict = null;

        // 从应用资源直接获取主题资源字典
        string themeDictKey = isDarkMode ? "DarkThemeDict" : "LightThemeDict";
        if (app.Resources.Contains(themeDictKey))
        {
            themeDict = app.Resources[themeDictKey] as ResourceDictionary;
        }

        if (themeDict == null) return;

        // 查找MaterialDesign主题并设置BaseTheme
        foreach (var dict in mergedDicts)
        {
            if (dict is BundledTheme materialTheme)
            {
                materialTheme.BaseTheme = isDarkMode ? BaseTheme.Dark : BaseTheme.Light;
                break;
            }
        }

        // 合并主题资源到应用资源
        foreach (var key in themeDict.Keys)
        {
            if (app.Resources.Contains(key))
            {
                app.Resources[key] = themeDict[key];
            }
            else
            {
                app.Resources.Add(key, themeDict[key]);
            }
        }

        // 触发主题变更事件
        SystemThemeChanged?.Invoke(app, isDarkMode);
    }

    /// <summary>
    /// 系统主题变化事件处理
    /// </summary>
    private void SystemEvents_UserPreferenceChanged(object sender, UserPreferenceChangedEventArgs e)
    {
        if (e.Category == UserPreferenceCategory.General || e.Category == UserPreferenceCategory.VisualStyle || e.Category == UserPreferenceCategory.Color)
        {
            // 检测主题变化
            bool currentIsDarkMode = IsSystemDarkModeEnabled();
            if (currentIsDarkMode != IsDarkMode)
            {
                IsDarkMode = currentIsDarkMode;
                ApplyTheme(currentIsDarkMode);
                Console.WriteLine($"系统主题已变更为: {(currentIsDarkMode ? "深色模式" : "浅色模式")}");
            }
        }
    }

    /// <summary>
    /// 输出渲染能力信息
    /// </summary>
    private void LogRenderCapabilities()
    {
        try
        {
            var renderCapability = RenderCapability.Tier >> 16;
            Console.WriteLine($"渲染层级: Tier {renderCapability}");

            if (RenderCapability.IsPixelShaderVersionSupported(3, 0))
            {
                Console.WriteLine("支持Pixel Shader 3.0");
            }

            if (RenderCapability.IsPixelShaderVersionSupported(2, 0))
            {
                Console.WriteLine("支持Pixel Shader 2.0");
            }

            Console.WriteLine($"最大纹理大小: {RenderCapability.MaxHardwareTextureSize}");
            Console.WriteLine($"硬件加速: {RenderOptions.ProcessRenderMode == RenderMode.Default}");

            // 检测GPU信息
            using (var searcher = new System.Management.ManagementObjectSearcher("SELECT * FROM Win32_VideoController"))
            {
                foreach (var obj in searcher.Get())
                {
                    Console.WriteLine($"GPU: {obj["Name"]}");
                    Console.WriteLine($"驱动版本: {obj["DriverVersion"]}");
                    Console.WriteLine($"显存: {obj["AdapterRAM"]} bytes");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取渲染能力信息时发生错误: {ex.Message}");
        }
    }
}
