using Microsoft.Extensions.Configuration;
using System;
using System.IO;

namespace SimpleBinanceRanking.Services
{
    /// <summary>
    /// 配置服务实现，从appsettings.json读取配置
    /// </summary>
    public class ConfigurationService : IConfigurationService
    {
        private readonly IConfiguration _configuration;
        private readonly WebSocketConfig _webSocketConfig;

        /// <summary>
        /// 获取币安REST API基础URL
        /// </summary>
        public string BinanceRestApiBaseUrl => _configuration["Binance:RestApiBaseUrl"] ?? "https://fapi.binance.com";

        /// <summary>
        /// 获取币安WebSocket基础URL
        /// </summary>
        public string BinanceWebSocketBaseUrl => _configuration["Binance:WebSocketBaseUrl"] ?? "wss://fstream.binance.com/ws";

        /// <summary>
        /// 获取WebSocket配置
        /// </summary>
        public WebSocketConfig WebSocketConfig => _webSocketConfig;

        /// <summary>
        /// 构造函数
        /// </summary>
        public ConfigurationService()
        {
            // 创建配置构建器
            var builder = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory());

            // 检查特定配置文件是否存在
            string specificConfigFile = "appsettings.test.json";
            bool specificConfigExists = File.Exists(Path.Combine(Directory.GetCurrentDirectory(), specificConfigFile));

            if (specificConfigExists)
            {
                // 如果特定配置文件存在，则加载它
                builder.AddJsonFile(specificConfigFile, optional: false, reloadOnChange: true);
                Console.WriteLine($"已加载特定配置文件: {specificConfigFile}");
            }
            else
            {
                // 如果特定配置文件不存在，则加载默认配置文件
                builder.AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
                Console.WriteLine("已加载默认配置文件: appsettings.json");
            }

            // 构建配置
            _configuration = builder.Build();

            // 加载WebSocket配置
            _webSocketConfig = new WebSocketConfig();
            _configuration.GetSection("Binance:WebSocketConfig").Bind(_webSocketConfig);

            Console.WriteLine($"当前配置 - REST API URL: {BinanceRestApiBaseUrl}, WebSocket URL: {BinanceWebSocketBaseUrl}");
            Console.WriteLine($"WebSocket配置 - 重连超时: {_webSocketConfig.ReconnectTimeoutSeconds}秒, 最大重连次数: {_webSocketConfig.MaxReconnectAttempts}, 心跳间隔: {_webSocketConfig.HeartbeatIntervalMinutes}分钟");
        }
    }
}
