using System;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Interop;
using Microsoft.Win32;

namespace SimpleBinanceRanking.Services
{
    /// <summary>
    /// 窗口主题服务，专门处理窗口标题栏颜色
    /// </summary>
    public static class WindowThemeService
    {
        // DWMWA_* 常量定义
        private const int DWMWA_USE_IMMERSIVE_DARK_MODE = 20; // Win10 1809以上版本支持
        private const int DWMWA_CAPTION_COLOR = 35; // 标题栏颜色，仅在某些Windows版本支持
        private const int DWMWA_SYSTEMBACKDROP_TYPE = 38; // 系统背景类型，Windows 11支持

        // DWM API - 设置窗口属性
        [DllImport("dwmapi.dll")]
        private static extern int DwmSetWindowAttribute(IntPtr hwnd, int attr, ref int attrValue, int attrSize);

        // 注册表相关常量
        private const string REGISTRY_KEY_PERSONALIZE = @"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize";
        private const string REGISTRY_VALUE_APPS_LIGHT_THEME = "AppsUseLightTheme";

        /// <summary>
        /// 应用深色/浅色模式到窗口标题栏
        /// </summary>
        /// <param name="window">要应用主题的窗口</param>
        /// <param name="isDarkMode">是否为深色模式</param>
        public static void ApplyThemeToTitleBar(Window window, bool isDarkMode)
        {
            try
            {
                // 获取窗口句柄
                var windowHelper = new WindowInteropHelper(window);
                var hwnd = windowHelper.Handle;
                
                if (hwnd == IntPtr.Zero)
                {
                    Console.WriteLine("无法获取窗口句柄，窗口可能尚未初始化");
                    // 当窗口加载完成后再尝试
                    window.SourceInitialized += (s, e) =>
                    {
                        hwnd = new WindowInteropHelper(window).Handle;
                        if (hwnd != IntPtr.Zero)
                        {
                            ApplyDarkModeToTitleBar(hwnd, isDarkMode);
                        }
                    };
                    return;
                }

                ApplyDarkModeToTitleBar(hwnd, isDarkMode);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"应用主题到标题栏时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 通过DWM API应用深色模式到标题栏
        /// </summary>
        private static void ApplyDarkModeToTitleBar(IntPtr hwnd, bool isDarkMode)
        {
            try
            {
                // 对于Windows 10 1809以上版本，设置深色模式
                int darkModeValue = isDarkMode ? 1 : 0;
                Console.WriteLine($"设置窗口标题栏主题: {(isDarkMode ? "深色模式" : "浅色模式")}");
                
                // 尝试使用DWMWA_USE_IMMERSIVE_DARK_MODE设置标题栏主题
                DwmSetWindowAttribute(hwnd, DWMWA_USE_IMMERSIVE_DARK_MODE, ref darkModeValue, sizeof(int));
                
                // 在Windows 11上尝试设置标题栏颜色
                // 注意：这些高级设置可能在某些Windows版本上不受支持
                if (Environment.OSVersion.Version.Build >= 22000) // Windows 11
                {
                    Console.WriteLine("检测到Windows 11，尝试设置额外主题属性");
                    
                    // 设置系统背景类型
                    int backdropType = isDarkMode ? 2 : 1; // 2=深色，1=浅色
                    DwmSetWindowAttribute(hwnd, DWMWA_SYSTEMBACKDROP_TYPE, ref backdropType, sizeof(int));
                }
                
                Console.WriteLine("已应用标题栏主题设置");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置窗口属性时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 检测当前系统是否使用深色模式
        /// 这是最可靠的检测方法
        /// </summary>
        public static bool IsSystemUsingDarkMode()
        {
            try
            {
                using var key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY_PERSONALIZE);
                if (key == null) return true; // 默认深色模式
                
                var value = key.GetValue(REGISTRY_VALUE_APPS_LIGHT_THEME);
                if (value == null) return true; // 默认深色模式
                
                // 0 = 深色模式，1 = 浅色模式
                return (int)value == 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检测系统主题出错: {ex.Message}");
                return true; // 出错时默认使用深色模式
            }
        }
    }
}
