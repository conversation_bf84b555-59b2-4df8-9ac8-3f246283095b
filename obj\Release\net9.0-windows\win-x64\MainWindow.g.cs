﻿#pragma checksum "..\..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7C031C12DCF320B0B2638EC9C3D84E7C724D9F70"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using SimpleBinanceRanking;
using SimpleBinanceRanking.ValueConverters;
using SimpleBinanceRanking.ViewModels;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SimpleBinanceRanking {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 138 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MainGrid;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SymbolSearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border BottomColorLine;
        
        #line default
        #line hidden
        
        
        #line 208 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.LinearGradientBrush BottomFlowingGradient;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.GradientStop BottomColor1;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.GradientStop BottomColor2;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.GradientStop BottomColor3;
        
        #line default
        #line hidden
        
        
        #line 212 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.GradientStop BottomColor4;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.GradientStop BottomColor5;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.GradientStop BottomColor6;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.GradientStop BottomColor7;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.GradientStop BottomColor8;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.GradientStop BottomColor9;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.Animation.BeginStoryboard BottomFlowAnimation;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse ConnectionStatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 251 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConnectionStatusText;
        
        #line default
        #line hidden
        
        
        #line 253 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DisconnectButton;
        
        #line default
        #line hidden
        
        
        #line 298 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView PriceChangePercentRankingList;
        
        #line default
        #line hidden
        
        
        #line 390 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView PriceChangePercentDescRankingList;
        
        #line default
        #line hidden
        
        
        #line 482 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView PriceVolatilityRankingList;
        
        #line default
        #line hidden
        
        
        #line 574 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView VolumeRankingList;
        
        #line default
        #line hidden
        
        
        #line 666 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView CountRankingList;
        
        #line default
        #line hidden
        
        
        #line 742 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConnectionDurationText;
        
        #line default
        #line hidden
        
        
        #line 747 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RemainingReconnectTimeText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleBinanceRanking;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 28 "..\..\..\..\MainWindow.xaml"
            ((SimpleBinanceRanking.MainWindow)(target)).Loaded += new System.Windows.RoutedEventHandler(this.Window_Loaded);
            
            #line default
            #line hidden
            return;
            case 3:
            this.MainGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 4:
            this.SymbolSearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 174 "..\..\..\..\MainWindow.xaml"
            this.SymbolSearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SymbolSearchTextBox_TextChanged);
            
            #line default
            #line hidden
            
            #line 175 "..\..\..\..\MainWindow.xaml"
            this.SymbolSearchTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.SymbolSearchTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 176 "..\..\..\..\MainWindow.xaml"
            this.SymbolSearchTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.SymbolSearchTextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BottomColorLine = ((System.Windows.Controls.Border)(target));
            return;
            case 6:
            this.BottomFlowingGradient = ((System.Windows.Media.LinearGradientBrush)(target));
            return;
            case 7:
            this.BottomColor1 = ((System.Windows.Media.GradientStop)(target));
            return;
            case 8:
            this.BottomColor2 = ((System.Windows.Media.GradientStop)(target));
            return;
            case 9:
            this.BottomColor3 = ((System.Windows.Media.GradientStop)(target));
            return;
            case 10:
            this.BottomColor4 = ((System.Windows.Media.GradientStop)(target));
            return;
            case 11:
            this.BottomColor5 = ((System.Windows.Media.GradientStop)(target));
            return;
            case 12:
            this.BottomColor6 = ((System.Windows.Media.GradientStop)(target));
            return;
            case 13:
            this.BottomColor7 = ((System.Windows.Media.GradientStop)(target));
            return;
            case 14:
            this.BottomColor8 = ((System.Windows.Media.GradientStop)(target));
            return;
            case 15:
            this.BottomColor9 = ((System.Windows.Media.GradientStop)(target));
            return;
            case 16:
            this.BottomFlowAnimation = ((System.Windows.Media.Animation.BeginStoryboard)(target));
            return;
            case 17:
            this.ConnectionStatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 18:
            this.ConnectionStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.DisconnectButton = ((System.Windows.Controls.Button)(target));
            
            #line 253 "..\..\..\..\MainWindow.xaml"
            this.DisconnectButton.Click += new System.Windows.RoutedEventHandler(this.DisconnectButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.PriceChangePercentRankingList = ((System.Windows.Controls.ListView)(target));
            
            #line 299 "..\..\..\..\MainWindow.xaml"
            this.PriceChangePercentRankingList.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.RankingList_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 312 "..\..\..\..\MainWindow.xaml"
            this.PriceChangePercentRankingList.Loaded += new System.Windows.RoutedEventHandler(this.ListView_Loaded);
            
            #line default
            #line hidden
            return;
            case 21:
            this.PriceChangePercentDescRankingList = ((System.Windows.Controls.ListView)(target));
            
            #line 391 "..\..\..\..\MainWindow.xaml"
            this.PriceChangePercentDescRankingList.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.RankingList_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 404 "..\..\..\..\MainWindow.xaml"
            this.PriceChangePercentDescRankingList.Loaded += new System.Windows.RoutedEventHandler(this.ListView_Loaded);
            
            #line default
            #line hidden
            return;
            case 22:
            this.PriceVolatilityRankingList = ((System.Windows.Controls.ListView)(target));
            
            #line 483 "..\..\..\..\MainWindow.xaml"
            this.PriceVolatilityRankingList.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.RankingList_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 496 "..\..\..\..\MainWindow.xaml"
            this.PriceVolatilityRankingList.Loaded += new System.Windows.RoutedEventHandler(this.ListView_Loaded);
            
            #line default
            #line hidden
            return;
            case 23:
            this.VolumeRankingList = ((System.Windows.Controls.ListView)(target));
            
            #line 575 "..\..\..\..\MainWindow.xaml"
            this.VolumeRankingList.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.RankingList_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 588 "..\..\..\..\MainWindow.xaml"
            this.VolumeRankingList.Loaded += new System.Windows.RoutedEventHandler(this.ListView_Loaded);
            
            #line default
            #line hidden
            return;
            case 24:
            this.CountRankingList = ((System.Windows.Controls.ListView)(target));
            
            #line 667 "..\..\..\..\MainWindow.xaml"
            this.CountRankingList.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.RankingList_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 680 "..\..\..\..\MainWindow.xaml"
            this.CountRankingList.Loaded += new System.Windows.RoutedEventHandler(this.ListView_Loaded);
            
            #line default
            #line hidden
            return;
            case 25:
            this.ConnectionDurationText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.RemainingReconnectTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            System.Windows.EventSetter eventSetter;
            switch (connectionId)
            {
            case 2:
            eventSetter = new System.Windows.EventSetter();
            eventSetter.Event = System.Windows.UIElement.MouseEnterEvent;
            
            #line 68 "..\..\..\..\MainWindow.xaml"
            eventSetter.Handler = new System.Windows.Input.MouseEventHandler(this.ListViewItem_MouseEnter);
            
            #line default
            #line hidden
            ((System.Windows.Style)(target)).Setters.Add(eventSetter);
            eventSetter = new System.Windows.EventSetter();
            eventSetter.Event = System.Windows.UIElement.MouseLeaveEvent;
            
            #line 69 "..\..\..\..\MainWindow.xaml"
            eventSetter.Handler = new System.Windows.Input.MouseEventHandler(this.ListViewItem_MouseLeave);
            
            #line default
            #line hidden
            ((System.Windows.Style)(target)).Setters.Add(eventSetter);
            break;
            }
        }
    }
}

