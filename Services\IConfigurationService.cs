using System;

namespace SimpleBinanceRanking.Services
{
    /// <summary>
    /// 配置服务接口，提供应用程序配置访问
    /// </summary>
    public interface IConfigurationService
    {
        /// <summary>
        /// 获取币安REST API基础URL
        /// </summary>
        string BinanceRestApiBaseUrl { get; }

        /// <summary>
        /// 获取币安WebSocket基础URL
        /// </summary>
        string BinanceWebSocketBaseUrl { get; }

        /// <summary>
        /// 获取WebSocket配置
        /// </summary>
        WebSocketConfig WebSocketConfig { get; }
    }

    /// <summary>
    /// WebSocket配置类
    /// </summary>
    public class WebSocketConfig
    {
        public int ConnectionLifetimeHours { get; set; } = 24;
        public int PingIntervalMinutes { get; set; } = 3;
        public int PongTimeoutMinutes { get; set; } = 10;
        public int MaxSubscriptionsPerConnection { get; set; } = 1024;
        public int MaxMessagesPerSecond { get; set; } = 10;
        public int ReconnectTimeoutSeconds { get; set; } = 30;
        public int ErrorReconnectTimeoutSeconds { get; set; } = 30;
        public int MaxReconnectAttempts { get; set; } = 10;
        public int HeartbeatIntervalMinutes { get; set; } = 2;
        public int MessageTimeoutSeconds { get; set; } = 120;
        public bool EnableAutoReconnect { get; set; } = true;
        public bool EnableConnectionLifecycleManagement { get; set; } = true;
        public bool EnableProactiveHeartbeat { get; set; } = true;
    }
}
