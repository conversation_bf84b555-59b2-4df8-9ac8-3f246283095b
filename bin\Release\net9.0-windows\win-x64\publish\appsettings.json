{"_Comments": "生产环境 - 直连币安交易所API，符合官方WebSocket规范", "Binance": {"RestApiBaseUrl": "https://fapi.binance.com", "WebSocketBaseUrl": "wss://fstream.binance.com/ws", "WebSocketConfig": {"ConnectionLifetimeHours": 24, "PingIntervalMinutes": 3, "PongTimeoutMinutes": 10, "MaxSubscriptionsPerConnection": 1024, "MaxMessagesPerSecond": 10, "ReconnectTimeoutSeconds": 30, "ErrorReconnectTimeoutSeconds": 30, "MaxReconnectAttempts": 10, "HeartbeatIntervalMinutes": 2, "MessageTimeoutSeconds": 120, "EnableAutoReconnect": true, "EnableConnectionLifecycleManagement": true, "EnableProactiveHeartbeat": true}}, "Logging": {"LogLevel": {"Default": "Information", "WebSocket": "Information", "Heartbeat": "Information", "ConnectionLifecycle": "Information"}}, "Monitoring": {"EnableConnectionStats": true, "StatsOutputIntervalMinutes": 5, "AlertOnConnectionIssues": true, "MaxMessageGapMinutes": 2}}