using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using Newtonsoft.Json;

namespace SimpleBinanceRanking.Models
{
    /// <summary>
    /// 排名数据模型，用于各种排名展示
    /// </summary>
    public class RankingData : INotifyPropertyChanged
    {
        /// <summary>
        /// 排名序号
        /// </summary>
        [JsonProperty("rank")]
        public int Rank { get; set; }

        /// <summary>
        /// 交易对符号
        /// </summary>
        [JsonProperty("symbol")]
        public string Symbol { get; set; } = string.Empty;

        /// <summary>
        /// 排名值（根据不同排名类型有不同含义）
        /// </summary>
        [JsonProperty("value")]
        public double Value { get; set; }

        /// <summary>
        /// 当前价格
        /// </summary>
        [JsonProperty("price")]
        public double Price { get; set; }

        /// <summary>
        /// 数据名称（用于显示在排行榜中）
        /// </summary>
        [JsonProperty("dataName")]
        public string DataName { get; set; } = string.Empty;

        private bool _isHighlighted;
        /// <summary>
        /// 是否高亮显示
        /// </summary>
        [JsonIgnore]
        public bool IsHighlighted
        {
            get => _isHighlighted;
            set
            {
                if (_isHighlighted != value)
                {
                    _isHighlighted = value;
                    OnPropertyChanged();
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName ?? string.Empty));
        }

        /// <summary>
        /// 创建RankingData对象
        /// </summary>
        /// <param name="symbol">交易对符号</param>
        /// <param name="value">排名值</param>
        /// <param name="price">当前价格</param>
        /// <param name="rank">排名序号</param>
        /// <param name="dataName">数据名称</param>
        public RankingData(string symbol, double value, double price, int rank = 0, string dataName = "")
        {
            // 如果Symbol以USDT结尾，去掉USDT后缀，使显示更简洁
            Symbol = symbol.EndsWith("USDT") ? symbol.Substring(0, symbol.Length - 4) : symbol;
            Value = value;
            Price = price;
            Rank = rank;
            DataName = dataName;
        }
    }
}



