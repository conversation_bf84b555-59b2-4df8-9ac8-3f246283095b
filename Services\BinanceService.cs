using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.WebSockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using SimpleBinanceRanking.Models;
using Websocket.Client;
using System.Reactive.Linq;

namespace SimpleBinanceRanking.Services
{
    /// <summary>
    /// 币安服务实现，处理与币安API的交互
    /// </summary>
    public class BinanceService : IBinanceService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfigurationService _configService;
        private WebsocketClient? _websocketClient;
        private string _connectionStatus = "未连接";
        private Action<List<TickerData>>? _messageHandler;
        private System.Threading.Timer? _reconnectTimer;
        private bool _isDisposed = false;

        // 添加重试相关配置
        private const int MAX_RETRIES = 3;
        private const int RETRY_DELAY_MS = 2000; // 每次重试等待时间（毫秒）
        private static readonly Random _jitter = new Random(); // 用于添加随机性，避免集中请求

        // WebSocket重连配置 - 从配置文件读取
        private int _reconnectAttempts = 0;

        // 连接生命周期管理
        private DateTime _connectionStartTime;
        private System.Threading.Timer? _connectionLifetimeTimer;
        private System.Threading.Timer? _heartbeatTimer;
        private DateTime _lastPongSent = DateTime.MinValue;
        private bool _isFirstConnection = true; // 标记是否为首次连接

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="httpClient">HTTP客户端</param>
        /// <param name="configService">配置服务</param>
        public BinanceService(HttpClient httpClient, IConfigurationService configService)
        {
            _httpClient = httpClient;
            _configService = configService;

            // 配置请求头，使其更像正常浏览器
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36");
            _httpClient.DefaultRequestHeaders.Add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            _httpClient.DefaultRequestHeaders.Add("Cache-Control", "no-cache");
            _httpClient.DefaultRequestHeaders.Add("Pragma", "no-cache");
            _httpClient.DefaultRequestHeaders.Add("X-MBX-APIKEY", ""); // 如果有API密钥可以在这里设置

            // 设置超时时间
            _httpClient.Timeout = TimeSpan.FromSeconds(30);

            Console.WriteLine($"BinanceService已初始化，使用REST API URL: {_configService.BinanceRestApiBaseUrl}");
        }

        /// <summary>
        /// 获取所有交易对的24小时行情数据，带有指数退避重试机制
        /// </summary>
        /// <returns>行情数据列表</returns>
        public async Task<List<TickerData>> GetTickerDataAsync()
        {
            int retryCount = 0;

            while (true) // 循环直到成功或重试次数超过限制
            {
                try
                {
                    // 添加时间戳参数以避免缓存
                    var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                    // 根据配置的URL构建完整请求URL
                    var url = $"{_configService.BinanceRestApiBaseUrl}/fapi/v1/ticker/24hr?timestamp={timestamp}";

                    // 如果是代理URL，需要调整路径格式
                    if (_configService.BinanceRestApiBaseUrl.Contains("8.211.164.212"))
                    {
                        url = $"{_configService.BinanceRestApiBaseUrl}/fapi/v1/ticker/24hr?timestamp={timestamp}";
                    }

                    // 发送请求前先防止请求过快
                    if (retryCount > 0)
                    {
                        // 指数退避策略：每次重试等待时间翻倍，并添加随机性
                        int delay = RETRY_DELAY_MS * (1 << (retryCount - 1)) + _jitter.Next(500);
                        await Task.Delay(delay);
                        Console.WriteLine($"第{retryCount}次重试获取数据，等待{delay}ms");
                    }

                    // 使用HttpClient发送请求
                    using (var request = new HttpRequestMessage(HttpMethod.Get, url))
                    {
                        // 每次请求都加上新的随机请求头
                        request.Headers.Add("X-Request-Id", Guid.NewGuid().ToString());

                        var response = await _httpClient.SendAsync(request);

                        // 如果响应是418，则需要重试
                        if (response.StatusCode == System.Net.HttpStatusCode.Locked ||
                            (int)response.StatusCode == 418 ||
                            (int)response.StatusCode == 429)
                        {
                            retryCount++;

                            if (retryCount > MAX_RETRIES)
                            {
                                Console.WriteLine($"获取数据错误: {response.StatusCode}");
                                throw new HttpRequestException($"Response status code does not indicate success: {(int)response.StatusCode} ({response.StatusCode})");
                            }

                            Console.WriteLine($"接收到限流响应 ({response.StatusCode})，准备重试");
                            continue;
                        }

                        // 检查其他错误
                        response.EnsureSuccessStatusCode();

                        // 处理成功响应
                        var content = await response.Content.ReadAsStringAsync();
                        var data = JsonConvert.DeserializeObject<List<TickerData>>(content) ?? new List<TickerData>();
                        Console.WriteLine($"REST API成功返回数据: 获取到 {data.Count} 个交易对的数据");
                        return data;
                    }
                }
                catch (HttpRequestException ex) when (retryCount < MAX_RETRIES)
                {
                    // 对于某些网络错误进行重试
                    retryCount++;
                    Console.WriteLine($"获取行情数据失败: {ex.Message}，准备第{retryCount}次重试");
                }
                catch (Exception ex)
                {
                    // 其他错误直接抛出
                    Console.WriteLine($"获取行情数据失败: {ex.Message}");
                    throw;
                }
            }
        }

        /// <summary>
        /// 连接到行情WebSocket
        /// </summary>
        /// <param name="onMessageReceived">消息接收回调</param>
        /// <returns>连接任务</returns>
        public async Task ConnectToTickerStreamAsync(Action<List<TickerData>> onMessageReceived)
        {
            try
            {
                _messageHandler = onMessageReceived;

                // 关闭现有连接
                if (_websocketClient != null)
                {
                    await _websocketClient.Stop(WebSocketCloseStatus.NormalClosure, "关闭之前的连接");
                    _websocketClient.Dispose();
                }

                _connectionStatus = "正在连接";
                _reconnectAttempts = 0;

                // 构建WebSocket连接URL - 符合币安官方规范
                string wsUrl = BuildWebSocketUrl();
                Console.WriteLine($"连接到WebSocket URL: {wsUrl}");

                var uri = new Uri(wsUrl);
                var config = _configService.WebSocketConfig;
                _websocketClient = new WebsocketClient(uri)
                {
                    ReconnectTimeout = TimeSpan.FromSeconds(config.ReconnectTimeoutSeconds),
                    ErrorReconnectTimeout = TimeSpan.FromSeconds(config.ErrorReconnectTimeoutSeconds),
                    IsReconnectionEnabled = config.EnableAutoReconnect
                };

                // 设置事件处理器
                SetupWebSocketEventHandlers();

                // 启动连接
                await _websocketClient.Start();

                // 每次连接都重新记录连接开始时间
                _connectionStartTime = DateTime.UtcNow;
                if (_isFirstConnection)
                {
                    _isFirstConnection = false;
                    Console.WriteLine($"首次连接建立，连接开始时间: {_connectionStartTime:yyyy-MM-dd HH:mm:ss} UTC");
                }
                else
                {
                    Console.WriteLine($"重连成功，重置连接开始时间: {_connectionStartTime:yyyy-MM-dd HH:mm:ss} UTC");
                }

                // 启动连接生命周期管理
                StartConnectionLifetimeManagement();

                // 启动心跳管理
                StartHeartbeatManagement();

                Console.WriteLine("WebSocket连接成功建立");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"连接WebSocket失败: {ex.Message}");
                _connectionStatus = "连接失败";
                throw;
            }
        }

        /// <summary>
        /// 构建符合币安官方规范的WebSocket URL
        /// </summary>
        /// <returns>WebSocket连接URL</returns>
        private string BuildWebSocketUrl()
        {
            var baseUrl = _configService.BinanceWebSocketBaseUrl;

            // 移除可能的尾部斜杠
            baseUrl = baseUrl.TrimEnd('/');

            // 根据官方文档，全市场ticker的stream名称是 !ticker@arr
            // URL格式: wss://fstream.binance.com/ws/!ticker@arr
            if (baseUrl.Contains("8.211.164.212"))
            {
                // 代理服务器可能需要特殊路径
                return $"{baseUrl}/fstream/ws/!ticker@arr";
            }
            else
            {
                // 标准币安WebSocket URL格式
                return $"{baseUrl}/!ticker@arr";
            }
        }

        /// <summary>
        /// 启动连接生命周期管理
        /// </summary>
        private void StartConnectionLifetimeManagement()
        {
            var config = _configService.WebSocketConfig;
            if (!config.EnableConnectionLifecycleManagement)
            {
                Console.WriteLine("连接生命周期管理已禁用");
                return;
            }

            // 停止现有定时器
            _connectionLifetimeTimer?.Dispose();

            // 设置配置的小时数后自动重连的定时器
            var lifetimeMs = config.ConnectionLifetimeHours * 60 * 60 * 1000;
            _connectionLifetimeTimer = new System.Threading.Timer(async _ =>
            {
                try
                {
                    Console.WriteLine($"连接已达到{config.ConnectionLifetimeHours}小时生命周期，开始重新连接...");
                    if (_messageHandler != null && !_isDisposed)
                    {
                        await ConnectToTickerStreamAsync(_messageHandler);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"{config.ConnectionLifetimeHours}小时重连失败: {ex.Message}");
                }
            }, null, lifetimeMs, Timeout.Infinite);

            Console.WriteLine($"连接生命周期管理已启动，将在{config.ConnectionLifetimeHours}小时后自动重连");
        }

        /// <summary>
        /// 启动心跳管理
        /// </summary>
        private void StartHeartbeatManagement()
        {
            var config = _configService.WebSocketConfig;
            if (!config.EnableProactiveHeartbeat)
            {
                Console.WriteLine("主动心跳已禁用");
                return;
            }

            // 停止现有心跳定时器
            _heartbeatTimer?.Dispose();

            // 根据配置设置心跳间隔
            var heartbeatIntervalMs = config.HeartbeatIntervalMinutes * 60 * 1000;

            _heartbeatTimer = new System.Threading.Timer(_ =>
            {
                try
                {
                    if (_websocketClient?.IsRunning == true)
                    {
                        // 发送pong帧保持连接活跃
                        var pongMessage = "{\"pong\":\"\"}";
                        _websocketClient.Send(pongMessage);
                        _lastPongSent = DateTime.UtcNow;
                        Console.WriteLine($"主动发送pong帧保持连接活跃 (间隔: {config.HeartbeatIntervalMinutes}分钟)");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"发送心跳pong失败: {ex.Message}");
                }
            }, null, heartbeatIntervalMs, heartbeatIntervalMs);

            Console.WriteLine($"心跳管理已启动，每{config.HeartbeatIntervalMinutes}分钟发送pong帧");
        }

        /// <summary>
        /// 设置WebSocket事件处理器
        /// </summary>
        private void SetupWebSocketEventHandlers()
        {
            if (_websocketClient == null) return;

            // 连接成功事件
            _websocketClient.ReconnectionHappened.Subscribe(info =>
            {
                _connectionStatus = "已连接";
                _reconnectAttempts = 0;
                Console.WriteLine($"WebSocket连接状态: {info.Type}");
            });

            // 断开连接事件
            _websocketClient.DisconnectionHappened.Subscribe(info =>
            {
                _connectionStatus = "连接断开";
                Console.WriteLine($"WebSocket断开连接: {info.Type}, 原因: {info.CloseStatus}");

                // 如果不是手动断开，启动重连逻辑
                if (info.Type != DisconnectionType.ByUser && !_isDisposed)
                {
                    StartReconnectTimer();
                }
            });

            // 消息接收事件
            _websocketClient.MessageReceived.Subscribe(msg =>
            {
                try
                {
                    var message = msg.Text;

                    // 处理ping消息 - 根据币安官方文档
                    if (message?.Contains("\"ping\"") == true)
                    {
                        // 服务器发送ping，客户端必须在10分钟内回复pong
                        var pong = message.Replace("\"ping\"", "\"pong\"");
                        _websocketClient.Send(pong);
                        _lastPongSent = DateTime.UtcNow;
                        Console.WriteLine($"收到服务器ping，已回复pong (时间: {_lastPongSent:HH:mm:ss})");
                        return;
                    }

                    // 处理订阅确认消息
                    if (message?.Contains("\"result\"") == true && message.Contains("\"id\""))
                    {
                        Console.WriteLine($"收到订阅确认消息: {message}");
                        return;
                    }

                    // 解析行情数据
                    if (!string.IsNullOrEmpty(message))
                    {
                        ProcessTickerData(message);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"处理WebSocket消息失败: {ex.Message}");
                    var textLength = msg.Text?.Length ?? 0;
                    Console.WriteLine($"消息内容: {msg.Text?.Substring(0, Math.Min(200, textLength))}...");
                }
            });
        }

        /// <summary>
        /// 处理ticker数据
        /// </summary>
        /// <param name="message">WebSocket消息</param>
        private void ProcessTickerData(string message)
        {
            try
            {
                var tickers = JsonConvert.DeserializeObject<List<dynamic>>(message);
                var tickerDataList = new List<TickerData>();

                if (tickers != null)
                {
                    foreach (var ticker in tickers)
                    {
                        tickerDataList.Add(TickerData.UpdateFromWs(ticker));
                    }

                    // 只在有数据时输出日志，避免日志过多
                    if (tickerDataList.Count > 0)
                    {
                        Console.WriteLine($"WebSocket成功接收数据: 获取到 {tickerDataList.Count} 个交易对的实时数据");
                    }
                }

                _messageHandler?.Invoke(tickerDataList);
            }
            catch (JsonException jsonEx)
            {
                Console.WriteLine($"JSON解析失败: {jsonEx.Message}");
                Console.WriteLine($"消息内容: {message.Substring(0, Math.Min(500, message.Length))}...");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"处理ticker数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 启动重连定时器
        /// </summary>
        private void StartReconnectTimer()
        {
            var config = _configService.WebSocketConfig;
            if (_reconnectAttempts >= config.MaxReconnectAttempts)
            {
                Console.WriteLine($"已达到最大重连次数 {config.MaxReconnectAttempts}，停止重连");
                return;
            }

            _reconnectAttempts++;
            var maxDelayMs = config.ReconnectTimeoutSeconds * 1000;
            var delay = Math.Min(1000 * Math.Pow(2, _reconnectAttempts), maxDelayMs);

            Console.WriteLine($"将在 {delay}ms 后进行第 {_reconnectAttempts} 次重连");

            _reconnectTimer?.Dispose();
            _reconnectTimer = new System.Threading.Timer(async _ =>
            {
                try
                {
                    if (!_isDisposed && _messageHandler != null)
                    {
                        Console.WriteLine($"开始第 {_reconnectAttempts} 次重连...");
                        await ConnectToTickerStreamAsync(_messageHandler);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"重连失败: {ex.Message}");
                }
            }, null, (int)delay, Timeout.Infinite);
        }

        /// <summary>
        /// 断开WebSocket连接
        /// </summary>
        public void DisconnectWebSockets()
        {
            try
            {
                Console.WriteLine("开始断开WebSocket连接...");
                _isDisposed = true;

                // 重置首次连接标志，下次连接时将重新计算连接开始时间
                _isFirstConnection = true;

                // 停止所有定时器
                StopAllTimers();

                // 关闭WebSocket连接
                if (_websocketClient != null)
                {
                    Console.WriteLine($"关闭WebSocket连接，当前状态: {_websocketClient.IsRunning}");

                    try
                    {
                        if (_websocketClient.IsRunning)
                        {
                            // 使用超时机制避免无限等待
                            var closeTask = _websocketClient.Stop(WebSocketCloseStatus.NormalClosure, "手动关闭连接");
                            var timeoutTask = Task.Delay(3000); // 3秒超时

                            if (Task.WhenAny(closeTask, timeoutTask).Result == closeTask)
                            {
                                Console.WriteLine("WebSocket正常关闭");
                            }
                            else
                            {
                                Console.WriteLine("WebSocket关闭超时");
                            }
                        }
                    }
                    catch (Exception closeEx)
                    {
                        Console.WriteLine($"关闭WebSocket时出错: {closeEx.Message}");
                    }
                    finally
                    {
                        // 无论如何都要释放资源
                        _websocketClient.Dispose();
                        _websocketClient = null;
                    }
                }

                _connectionStatus = "已断开";
                Console.WriteLine("已完成WebSocket连接断开操作");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"断开连接失败: {ex.Message}\n{ex.StackTrace}");
                _connectionStatus = "断开失败";
            }
        }

        /// <summary>
        /// 停止所有定时器
        /// </summary>
        private void StopAllTimers()
        {
            try
            {
                if (_reconnectTimer != null)
                {
                    Console.WriteLine("停止重连定时器...");
                    _reconnectTimer.Dispose();
                    _reconnectTimer = null;
                }

                if (_connectionLifetimeTimer != null)
                {
                    Console.WriteLine("停止连接生命周期定时器...");
                    _connectionLifetimeTimer.Dispose();
                    _connectionLifetimeTimer = null;
                }

                if (_heartbeatTimer != null)
                {
                    Console.WriteLine("停止心跳定时器...");
                    _heartbeatTimer.Dispose();
                    _heartbeatTimer = null;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"停止定时器时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取连接状态
        /// </summary>
        /// <returns>连接状态</returns>
        public string GetConnectionStatus()
        {
            // 检查WebSocket状态
            if (_websocketClient != null)
            {
                var config = _configService.WebSocketConfig;
                if (_websocketClient.IsRunning)
                {
                    // 计算连接时长
                    var connectionDuration = DateTime.UtcNow - _connectionStartTime;
                    var remainingHours = config.ConnectionLifetimeHours - connectionDuration.TotalHours;

                    if (remainingHours > 1)
                    {
                        _connectionStatus = $"已连接 (剩余{remainingHours:F1}小时)";
                    }
                    else if (remainingHours > 0)
                    {
                        _connectionStatus = $"已连接 (剩余{remainingHours * 60:F0}分钟)";
                    }
                    else
                    {
                        _connectionStatus = "已连接 (即将重连)";
                    }
                }
                else if (_isDisposed)
                {
                    _connectionStatus = "已断开";
                }
                else
                {
                    _connectionStatus = $"连接断开 (重连尝试: {_reconnectAttempts}/{config.MaxReconnectAttempts})";
                }
            }

            return _connectionStatus;
        }

        /// <summary>
        /// 获取连接统计信息
        /// </summary>
        /// <returns>连接统计信息</returns>
        public string GetConnectionStats()
        {
            if (_websocketClient?.IsRunning == true)
            {
                var connectionDuration = DateTime.UtcNow - _connectionStartTime;
                var lastPongAge = _lastPongSent == DateTime.MinValue ? "从未" :
                    $"{(DateTime.UtcNow - _lastPongSent).TotalMinutes:F1}分钟前";

                return $"连接时长: {connectionDuration.TotalHours:F1}小时, 最后pong: {lastPongAge}, 重连次数: {_reconnectAttempts}";
            }

            return "未连接";
        }

        /// <summary>
        /// 获取连接开始时间
        /// </summary>
        /// <returns>连接开始时间</returns>
        public DateTime GetConnectionStartTime()
        {
            return _connectionStartTime;
        }
    }
}
