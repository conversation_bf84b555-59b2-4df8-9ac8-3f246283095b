{"_Comments": "测试环境 - 通过代理访问API，符合官方WebSocket规范", "Binance": {"RestApiBaseUrl": "http://*************:8000", "WebSocketBaseUrl": "ws://*************:8000", "WebSocketConfig": {"ConnectionLifetimeHours": 24, "PingIntervalMinutes": 3, "PongTimeoutMinutes": 10, "MaxSubscriptionsPerConnection": 1024, "MaxMessagesPerSecond": 10, "ReconnectTimeoutSeconds": 15, "ErrorReconnectTimeoutSeconds": 15, "MaxReconnectAttempts": 15, "HeartbeatIntervalMinutes": 1, "MessageTimeoutSeconds": 60, "EnableAutoReconnect": true, "EnableConnectionLifecycleManagement": true, "EnableProactiveHeartbeat": true}}, "Logging": {"LogLevel": {"Default": "Information", "WebSocket": "Debug", "Heartbeat": "Debug", "ConnectionLifecycle": "Debug"}}, "Monitoring": {"EnableConnectionStats": true, "StatsOutputIntervalMinutes": 2, "AlertOnConnectionIssues": true, "MaxMessageGapMinutes": 1}}