using System;
using Newtonsoft.Json;

namespace SimpleBinanceRanking.Models
{
    /// <summary>
    /// 24小时行情数据模型，与币安API返回的数据结构匹配
    /// </summary>
    public class TickerData
    {
        [JsonProperty("symbol")]
        public string Symbol { get; set; } = string.Empty;

        [JsonProperty("priceChange")]
        public string PriceChange { get; set; } = string.Empty;

        [JsonProperty("priceChangePercent")]
        public string PriceChangePercent { get; set; } = string.Empty;

        [JsonProperty("weightedAvgPrice")]
        public string WeightedAvgPrice { get; set; } = string.Empty;

        [JsonProperty("lastPrice")]
        public string LastPrice { get; set; } = string.Empty;

        [JsonProperty("lastQty")]
        public string LastQty { get; set; } = string.Empty;

        [JsonProperty("openPrice")]
        public string OpenPrice { get; set; } = string.Empty;

        [JsonProperty("highPrice")]
        public string HighPrice { get; set; } = string.Empty;

        [JsonProperty("lowPrice")]
        public string LowPrice { get; set; } = string.Empty;

        [JsonProperty("volume")]
        public string Volume { get; set; } = string.Empty;

        [JsonProperty("quoteVolume")]
        public string QuoteVolume { get; set; } = string.Empty;

        [JsonProperty("openTime")]
        public long OpenTime { get; set; }

        [JsonProperty("closeTime")]
        public long CloseTime { get; set; }

        [JsonProperty("firstId")]
        public long FirstId { get; set; }

        [JsonProperty("lastId")]
        public long LastId { get; set; }

        [JsonProperty("count")]
        public int Count { get; set; }

        /// <summary>
        /// 从WebSocket消息更新TickerData对象
        /// </summary>
        /// <param name="wsData">WebSocket消息数据</param>
        /// <returns>更新后的TickerData对象</returns>
        public static TickerData UpdateFromWs(dynamic wsData)
        {
            return new TickerData
            {
                Symbol = wsData.s,
                PriceChange = wsData.p,
                PriceChangePercent = wsData.P,
                WeightedAvgPrice = wsData.w,
                LastPrice = wsData.c,
                LastQty = wsData.Q,
                OpenPrice = wsData.o,
                HighPrice = wsData.h,
                LowPrice = wsData.l,
                Volume = wsData.v,
                QuoteVolume = wsData.q,
                OpenTime = wsData.O,
                CloseTime = wsData.C,
                FirstId = wsData.F,
                LastId = wsData.L,
                Count = wsData.n
            };
        }
    }
}
