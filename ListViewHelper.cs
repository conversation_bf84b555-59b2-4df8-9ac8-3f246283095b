using System.Windows;
using System.Windows.Controls;

namespace SimpleBinanceRanking
{
    /// <summary>
    /// 提供ListView相关的附加属性
    /// </summary>
    public class ListViewHelper
    {
        // 定义ListViewName附加属性
        public static readonly DependencyProperty ListViewNameProperty =
            DependencyProperty.RegisterAttached(
                "ListViewName",
                typeof(string),
                typeof(ListViewHelper),
                new PropertyMetadata(string.Empty));

        // 定义ListViewIndex附加属性
        public static readonly DependencyProperty ListViewIndexProperty =
            DependencyProperty.RegisterAttached(
                "ListViewIndex",
                typeof(int),
                typeof(ListViewHelper),
                new PropertyMetadata(-1));

        // 获取ListViewName
        public static string GetListViewName(DependencyObject obj)
        {
            return (string)obj.GetValue(ListViewNameProperty);
        }

        // 设置ListViewName
        public static void SetListViewName(DependencyObject obj, string value)
        {
            obj.SetValue(ListViewNameProperty, value);
        }

        // 获取ListViewIndex
        public static int GetListViewIndex(DependencyObject obj)
        {
            return (int)obj.GetValue(ListViewIndexProperty);
        }

        // 设置ListViewIndex
        public static void SetListViewIndex(DependencyObject obj, int value)
        {
            obj.SetValue(ListViewIndexProperty, value);
        }
    }
}
