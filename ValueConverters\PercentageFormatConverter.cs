using System;
using System.Globalization;
using System.Windows.Data;

namespace SimpleBinanceRanking.ValueConverters
{
    /// <summary>
    /// 百分比格式化转换器
    /// 正值显示+号，负值不显示+号
    /// </summary>
    public class PercentageFormatConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double percentage)
            {
                return percentage >= 0 ? 
                    $"+{percentage:N2}%" : 
                    $"{percentage:N2}%";
            }

            return "0.00%";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 