using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SimpleBinanceRanking.Models;

namespace SimpleBinanceRanking.Services
{
    /// <summary>
    /// 币安服务接口，定义与币安API交互的方法
    /// </summary>
    public interface IBinanceService
    {
        /// <summary>
        /// 获取所有交易对的24小时行情数据
        /// </summary>
        /// <returns>行情数据列表</returns>
        Task<List<TickerData>> GetTickerDataAsync();

        /// <summary>
        /// 连接到行情WebSocket
        /// </summary>
        /// <param name="onMessageReceived">消息接收回调</param>
        /// <returns>连接任务</returns>
        Task ConnectToTickerStreamAsync(Action<List<TickerData>> onMessageReceived);

        /// <summary>
        /// 断开WebSocket连接
        /// </summary>
        void DisconnectWebSockets();

        /// <summary>
        /// 获取连接状态
        /// </summary>
        /// <returns>连接状态</returns>
        string GetConnectionStatus();

        /// <summary>
        /// 获取连接统计信息
        /// </summary>
        /// <returns>连接统计信息</returns>
        string GetConnectionStats();

        /// <summary>
        /// 获取连接开始时间
        /// </summary>
        /// <returns>连接开始时间</returns>
        DateTime GetConnectionStartTime();
    }
}
